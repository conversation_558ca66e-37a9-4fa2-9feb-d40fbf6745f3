package com.official.fivegfastvpn.activity;

import android.app.AlertDialog;
import android.app.NotificationManager;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.official.fivegfastvpn.MainActivity;
import com.official.fivegfastvpn.R;
import com.official.fivegfastvpn.ads.AdCall;
import com.official.fivegfastvpn.ads.AdCode;
import com.official.fivegfastvpn.ads.Admob;
import com.official.fivegfastvpn.ads.AdsHelper;
import com.official.fivegfastvpn.pro.PremiumActivity;

/**
 * Activity that shows when user taps the 1-minute warning notification
 * Displays a custom dialog with options to watch ad or purchase premium
 */
public class VpnWarningActivity extends AppCompatActivity {
    
    private static final String TAG = "VpnWarningActivity";
    private int remainingTime;
    private AlertDialog warningDialog;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Get remaining time from intent
        remainingTime = getIntent().getIntExtra("remaining_time", 60);
        
        // Cancel the warning notification
        NotificationManager notificationManager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
        notificationManager.cancel(9999);
        
        // Show the warning dialog immediately
        showWarningDialog();
        
        Log.d(TAG, "VpnWarningActivity created with remaining time: " + remainingTime);
    }
    
    private void showWarningDialog() {
        try {
            // Inflate custom dialog layout
            LayoutInflater inflater = getLayoutInflater();
            View dialogView = inflater.inflate(R.layout.dialog_vpn_warning, null);
            
            // Find views
            TextView titleText = dialogView.findViewById(R.id.warning_title);
            TextView messageText = dialogView.findViewById(R.id.warning_message);
            Button watchAdButton = dialogView.findViewById(R.id.btn_watch_ad);
            Button upgradePremiumButton = dialogView.findViewById(R.id.btn_upgrade_premium);
            Button dismissButton = dialogView.findViewById(R.id.btn_dismiss);
            
            // Set dialog content
            titleText.setText("VPN Time Warning");
            messageText.setText(String.format("Only %d seconds remaining!\nChoose an option to continue:", remainingTime));
            
            // Create dialog
            AlertDialog.Builder builder = new AlertDialog.Builder(this, R.style.CustomAlertDialog);
            builder.setView(dialogView);
            builder.setCancelable(false);
            
            warningDialog = builder.create();
            
            // Set button click listeners
            watchAdButton.setOnClickListener(v -> {
                Log.d(TAG, "Watch Ad button clicked");
                watchAdButton.setEnabled(false);
                watchAdButton.setText("Loading...");
                
                // Load and show rewarded ad
                if (AdCode.rewardedAd != null) {
                    Admob.showRewarded(this, new AdCall() {
                        @Override
                        public void next() {
                            Log.d(TAG, "Reward ad completed successfully");
                            handleAdReward();
                        }
                        
                        @Override
                        public void failed() {
                            Log.e(TAG, "Failed to show reward ad");
                            runOnUiThread(() -> {
                                watchAdButton.setEnabled(true);
                                watchAdButton.setText("Watch Ad");
                                showToast("Failed to load ad. Please try again.");
                            });
                        }
                    });
                } else {
                    Log.d(TAG, "Rewarded ad not loaded, loading now");
                    Admob.loadRew(this);
                    runOnUiThread(() -> {
                        watchAdButton.setEnabled(true);
                        watchAdButton.setText("Watch Ad");
                        showToast("Ad not ready yet. Please try again in a moment.");
                    });
                }
            });
            
            upgradePremiumButton.setOnClickListener(v -> {
                Log.d(TAG, "Upgrade Premium button clicked");
                warningDialog.dismiss();
                
                // Open premium activity
                Intent premiumIntent = new Intent(this, PremiumActivity.class);
                startActivity(premiumIntent);
                finish();
            });
            
            dismissButton.setOnClickListener(v -> {
                Log.d(TAG, "Dismiss button clicked");
                warningDialog.dismiss();
                finish();
            });
            
            // Show dialog
            warningDialog.show();
            
        } catch (Exception e) {
            Log.e(TAG, "Error showing warning dialog", e);
            finish();
        }
    }
    
    private void handleAdReward() {
        try {
            if (AdsHelper.reward_time != null && !AdsHelper.reward_time.isEmpty()) {
                int minutes = Integer.parseInt(AdsHelper.reward_time);
                int additionalSeconds = minutes * 60;
                
                Log.d(TAG, "Adding " + minutes + " minutes (" + additionalSeconds + " seconds) to timer");
                
                // Broadcast the time extension to the main app
                Intent intent = new Intent("vpn_time_extended");
                intent.putExtra("additional_seconds", additionalSeconds);
                LocalBroadcastManager.getInstance(this).sendBroadcast(intent);
                
                runOnUiThread(() -> {
                    showToast("Added " + minutes + " minutes to your VPN time!");
                    warningDialog.dismiss();
                    
                    // Return to main activity
                    Intent mainIntent = new Intent(this, MainActivity.class);
                    mainIntent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
                    startActivity(mainIntent);
                    finish();
                });
                
            } else {
                Log.e(TAG, "Reward time not available");
                runOnUiThread(() -> {
                    showToast("Error extending time. Please try again.");
                    if (warningDialog != null) {
                        Button watchAdButton = warningDialog.findViewById(R.id.btn_watch_ad);
                        if (watchAdButton != null) {
                            watchAdButton.setEnabled(true);
                            watchAdButton.setText("Watch Ad");
                        }
                    }
                });
            }
        } catch (NumberFormatException e) {
            Log.e(TAG, "Error parsing reward time", e);
            runOnUiThread(() -> {
                showToast("Error extending time. Please try again.");
                if (warningDialog != null) {
                    Button watchAdButton = warningDialog.findViewById(R.id.btn_watch_ad);
                    if (watchAdButton != null) {
                        watchAdButton.setEnabled(true);
                        watchAdButton.setText("Watch Ad");
                    }
                }
            });
        }
    }
    
    private void showToast(String message) {
        android.widget.Toast.makeText(this, message, android.widget.Toast.LENGTH_SHORT).show();
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (warningDialog != null && warningDialog.isShowing()) {
            warningDialog.dismiss();
        }
    }
    
    @Override
    public void onBackPressed() {
        // Prevent back button from dismissing the dialog
        // User must choose an option
        Log.d(TAG, "Back button pressed - ignoring to force user choice");
    }
}
