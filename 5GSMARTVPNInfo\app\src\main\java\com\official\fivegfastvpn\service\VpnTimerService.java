package com.official.fivegfastvpn.service;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.util.Log;

import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.official.fivegfastvpn.MainActivity;
import com.official.fivegfastvpn.R;
import com.official.fivegfastvpn.activity.VpnWarningActivity;
import com.official.fivegfastvpn.pro.ProConfig;
import com.official.fivegfastvpn.utils.Pref;

/**
 * Background service to keep VPN timer running even when app is closed
 */
public class VpnTimerService extends Service {
    
    private static final String TAG = "VpnTimerService";
    private static final int NOTIFICATION_ID = 2001;
    private static final String CHANNEL_ID = "vpn_timer_channel";
    private static final String WARNING_CHANNEL_ID = "vpn_warning_channel";
    
    private Handler timerHandler;
    private Runnable timerRunnable;
    private long timerStartTime;
    private int remainingTimeInSeconds;
    private boolean isPremium;
    private Pref pref;
    private boolean hasShownWarning = false;

    // Service health check
    private Handler healthCheckHandler;
    private Runnable healthCheckRunnable;
    
    @Override
    public void onCreate() {
        super.onCreate();
        timerHandler = new Handler();
        healthCheckHandler = new Handler();
        pref = new Pref(this);
        createNotificationChannels();
        startHealthCheck();
        Log.d(TAG, "VpnTimerService created");
    }
    
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent != null) {
            long newTimerStartTime = intent.getLongExtra("timer_start_time", System.currentTimeMillis());
            int newRemainingTime = intent.getIntExtra("remaining_time", 0);
            boolean newIsPremium = intent.getBooleanExtra("is_premium", false);

            Log.d(TAG, "TIMER DEBUG: VpnTimerService.onStartCommand called");
            Log.d(TAG, "TIMER DEBUG: Received timerStartTime = " + newTimerStartTime);
            Log.d(TAG, "TIMER DEBUG: Received remainingTimeInSeconds = " + newRemainingTime);
            Log.d(TAG, "TIMER DEBUG: Received isPremium = " + newIsPremium);

            if (newRemainingTime <= 0 && !newIsPremium) {
                Log.e(TAG, "TIMER DEBUG: ERROR - Received 0 or negative remaining time for non-premium user!");
                Log.e(TAG, "TIMER DEBUG: This will cause immediate VPN disconnection!");
                // Don't start the timer if we have invalid time
                return START_NOT_STICKY;
            }

            // Update timer values
            timerStartTime = newTimerStartTime;
            remainingTimeInSeconds = newRemainingTime;
            isPremium = newIsPremium;
            hasShownWarning = false; // Reset warning flag for new timer

            Log.d(TAG, "Timer service started - Premium: " + isPremium +
                      ", Remaining: " + remainingTimeInSeconds + " seconds");

            // Stop existing timer before starting new one
            stopTimer();

            startForegroundService();
            startTimer();
        } else {
            Log.e(TAG, "TIMER DEBUG: onStartCommand called with null intent!");
        }

        return START_STICKY; // Restart service if killed
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        stopTimer();
        stopHealthCheck();
        Log.d(TAG, "VpnTimerService destroyed");
    }

    @Override
    public void onTaskRemoved(Intent rootIntent) {
        super.onTaskRemoved(rootIntent);
        Log.d(TAG, "Task removed - but service should continue running");

        // Check if VPN is still connected and timer should continue
        if (shouldContinueRunning()) {
            Log.d(TAG, "VPN still connected, keeping timer service alive");
            // Restart the service to ensure it continues running
            Intent restartIntent = new Intent(getApplicationContext(), VpnTimerService.class);
            restartIntent.putExtra("timer_start_time", timerStartTime);
            restartIntent.putExtra("remaining_time", remainingTimeInSeconds);
            restartIntent.putExtra("is_premium", isPremium);

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForegroundService(restartIntent);
            } else {
                startService(restartIntent);
            }
        } else {
            Log.d(TAG, "VPN not connected, allowing service to stop");
            stopSelf();
        }
    }

    private boolean shouldContinueRunning() {
        // Check if VPN is still connected by checking preferences
        try {
            Pref pref = new Pref(this);
            boolean isVpnConnected = pref.getVpnConnectionState();
            Log.d(TAG, "VPN connection state: " + isVpnConnected);
            return isVpnConnected && (isPremium || remainingTimeInSeconds > 0);
        } catch (Exception e) {
            Log.e(TAG, "Error checking VPN state", e);
            return false;
        }
    }
    
    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }
    
    private void createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            
            // Timer notification channel
            NotificationChannel timerChannel = new NotificationChannel(
                CHANNEL_ID,
                "VPN Timer",
                NotificationManager.IMPORTANCE_DEFAULT
            );
            timerChannel.setDescription("VPN timer background service");
            timerChannel.setShowBadge(true);
            timerChannel.setLockscreenVisibility(Notification.VISIBILITY_PUBLIC);
            timerChannel.enableLights(false);
            timerChannel.enableVibration(false);
            notificationManager.createNotificationChannel(timerChannel);
            
            // Warning notification channel
            NotificationChannel warningChannel = new NotificationChannel(
                WARNING_CHANNEL_ID,
                "VPN Warning",
                NotificationManager.IMPORTANCE_HIGH
            );
            warningChannel.setDescription("VPN time warning notifications");
            warningChannel.enableVibration(true);
            warningChannel.setLightColor(android.graphics.Color.RED);
            notificationManager.createNotificationChannel(warningChannel);
        }
    }
    
    private void startForegroundService() {
        Intent notificationIntent = new Intent(this, MainActivity.class);
        PendingIntent pendingIntent;
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            pendingIntent = PendingIntent.getActivity(this, 0, notificationIntent, PendingIntent.FLAG_MUTABLE);
        } else {
            pendingIntent = PendingIntent.getActivity(this, 0, notificationIntent, PendingIntent.FLAG_UPDATE_CURRENT);
        }
        
        Notification.Builder builder;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            builder = new Notification.Builder(this, CHANNEL_ID);
        } else {
            builder = new Notification.Builder(this);
        }
        
        String contentText = isPremium ? "VPN timer running" : "VPN timer: " + formatTime(remainingTimeInSeconds);
        
        Notification notification = builder
            .setContentTitle("VPN Timer Active")
            .setContentText(contentText)
            .setSmallIcon(R.drawable.fivegsmartvpn)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setAutoCancel(false)
            .setPriority(Notification.PRIORITY_DEFAULT)
            .setCategory(Notification.CATEGORY_SERVICE)
            .setVisibility(Notification.VISIBILITY_PUBLIC)
            .build();
        
        startForeground(NOTIFICATION_ID, notification);
        Log.d(TAG, "Foreground service started");
    }
    
    private void startTimer() {
        if (timerRunnable != null) {
            timerHandler.removeCallbacks(timerRunnable);
        }
        
        timerRunnable = new Runnable() {
            @Override
            public void run() {
                if (isPremium) {
                    // For premium users, just track elapsed time
                    long elapsedMillis = System.currentTimeMillis() - timerStartTime;
                    int elapsedSeconds = (int) (elapsedMillis / 1000);
                    
                    // Save state periodically
                    pref.saveVpnTimerState(timerStartTime, elapsedSeconds, true, 0);
                    
                    // Update notification
                    updateNotification("VPN timer: " + formatElapsedTime(elapsedSeconds));
                    
                    // Broadcast timer update
                    broadcastTimerUpdate(elapsedSeconds, true);
                    
                    timerHandler.postDelayed(this, 1000);
                } else {
                    // For non-premium users, countdown timer
                    if (remainingTimeInSeconds > 0) {
                        remainingTimeInSeconds--;
                        
                        // Save state
                        long elapsedMillis = System.currentTimeMillis() - timerStartTime;
                        int elapsedSeconds = (int) (elapsedMillis / 1000);
                        pref.saveVpnTimerState(timerStartTime, elapsedSeconds, false, remainingTimeInSeconds);
                        
                        // Update notification
                        updateNotification("VPN timer: " + formatTime(remainingTimeInSeconds));
                        
                        // Check for 1-minute warning
                        if (remainingTimeInSeconds == 60 && !hasShownWarning) {
                            showOneMinuteWarningNotification();
                            hasShownWarning = true;
                        }
                        
                        // Broadcast timer update
                        broadcastTimerUpdate(remainingTimeInSeconds, false);
                        
                        timerHandler.postDelayed(this, 1000);
                    } else {
                        // Time expired - disconnect VPN
                        Log.d(TAG, "Timer expired - broadcasting VPN disconnect");
                        broadcastVpnDisconnect();
                        stopSelf();
                    }
                }
            }
        };
        
        timerHandler.post(timerRunnable);
    }
    
    private void stopTimer() {
        if (timerRunnable != null) {
            timerHandler.removeCallbacks(timerRunnable);
            timerRunnable = null;
        }
    }
    
    private void updateNotification(String contentText) {
        try {
            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            
            Intent notificationIntent = new Intent(this, MainActivity.class);
            PendingIntent pendingIntent;
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                pendingIntent = PendingIntent.getActivity(this, 0, notificationIntent, PendingIntent.FLAG_MUTABLE);
            } else {
                pendingIntent = PendingIntent.getActivity(this, 0, notificationIntent, PendingIntent.FLAG_UPDATE_CURRENT);
            }
            
            Notification.Builder builder;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                builder = new Notification.Builder(this, CHANNEL_ID);
            } else {
                builder = new Notification.Builder(this);
            }
            
            Notification notification = builder
                .setContentTitle("VPN Timer Active")
                .setContentText(contentText)
                .setSmallIcon(R.drawable.fivegsmartvpn)
                .setContentIntent(pendingIntent)
                .setOngoing(true)
                .build();
            
            notificationManager.notify(NOTIFICATION_ID, notification);
        } catch (Exception e) {
            Log.e(TAG, "Error updating notification", e);
        }
    }
    
    private void showOneMinuteWarningNotification() {
        try {
            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            
            // Create intent for when notification is tapped
            Intent notificationIntent = new Intent(this, VpnWarningActivity.class);
            notificationIntent.putExtra("remaining_time", remainingTimeInSeconds);
            notificationIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
            
            PendingIntent pendingIntent;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                pendingIntent = PendingIntent.getActivity(this, 1, notificationIntent, PendingIntent.FLAG_MUTABLE);
            } else {
                pendingIntent = PendingIntent.getActivity(this, 1, notificationIntent, PendingIntent.FLAG_UPDATE_CURRENT);
            }
            
            // Build warning notification
            Notification.Builder builder;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                builder = new Notification.Builder(this, WARNING_CHANNEL_ID);
            } else {
                builder = new Notification.Builder(this);
            }
            
            builder.setSmallIcon(R.drawable.fivegsmartvpn)
                   .setContentTitle("VPN Time Warning")
                   .setContentText("Only 1 minute remaining! Tap to extend time.")
                   .setAutoCancel(true)
                   .setContentIntent(pendingIntent)
                   .setPriority(Notification.PRIORITY_HIGH)
                   .setDefaults(Notification.DEFAULT_ALL);
            
            notificationManager.notify(9999, builder.build());
            Log.d(TAG, "1-minute warning notification shown from service");
        } catch (Exception e) {
            Log.e(TAG, "Error showing warning notification", e);
        }
    }
    
    private void broadcastTimerUpdate(int timeValue, boolean isPremiumTimer) {
        Intent intent = new Intent("vpn_timer_update");
        intent.putExtra("time_value", timeValue);
        intent.putExtra("is_premium", isPremiumTimer);
        LocalBroadcastManager.getInstance(this).sendBroadcast(intent);
    }
    
    private void broadcastVpnDisconnect() {
        Intent intent = new Intent("vpn_timer_expired");
        LocalBroadcastManager.getInstance(this).sendBroadcast(intent);
    }
    
    private String formatTime(int seconds) {
        int hours = seconds / 3600;
        int minutes = (seconds % 3600) / 60;
        int secs = seconds % 60;
        return String.format("%02d:%02d:%02d", hours, minutes, secs);
    }
    
    private String formatElapsedTime(int seconds) {
        int hours = seconds / 3600;
        int minutes = (seconds % 3600) / 60;
        return String.format("%02d:%02d elapsed", hours, minutes);
    }

    /**
     * Start periodic health check to ensure service stays alive
     */
    private void startHealthCheck() {
        if (healthCheckRunnable != null) {
            healthCheckHandler.removeCallbacks(healthCheckRunnable);
        }

        healthCheckRunnable = new Runnable() {
            @Override
            public void run() {
                try {
                    // Check if service should still be running
                    if (shouldContinueRunning()) {
                        Log.d(TAG, "Health check: Service is healthy and should continue");

                        // Ensure we're still in foreground
                        if (timerStartTime > 0) {
                            updateNotification(isPremium ? "VPN timer running" : "VPN timer: " + formatTime(remainingTimeInSeconds));
                        }

                        // Schedule next health check in 30 seconds
                        healthCheckHandler.postDelayed(this, 30000);
                    } else {
                        Log.d(TAG, "Health check: Service should stop");
                        stopSelf();
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error in health check", e);
                    // Continue health checks even if there's an error
                    healthCheckHandler.postDelayed(this, 30000);
                }
            }
        };

        // Start first health check after 30 seconds
        healthCheckHandler.postDelayed(healthCheckRunnable, 30000);
        Log.d(TAG, "Health check started");
    }

    /**
     * Stop health check
     */
    private void stopHealthCheck() {
        if (healthCheckRunnable != null && healthCheckHandler != null) {
            healthCheckHandler.removeCallbacks(healthCheckRunnable);
            healthCheckRunnable = null;
            Log.d(TAG, "Health check stopped");
        }
    }
}
