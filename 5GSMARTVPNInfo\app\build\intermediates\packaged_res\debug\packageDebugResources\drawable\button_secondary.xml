<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Pressed state -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/secondary_pressed" />
            <corners android:radius="8dp" />
            <stroke
                android:width="1dp"
                android:color="@color/secondary_border" />
        </shape>
    </item>
    
    <!-- Normal state -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/secondary_background" />
            <corners android:radius="8dp" />
            <stroke
                android:width="1dp"
                android:color="@color/secondary_border" />
        </shape>
    </item>
    
</selector>
