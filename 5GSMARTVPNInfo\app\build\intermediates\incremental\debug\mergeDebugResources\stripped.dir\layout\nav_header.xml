<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:background="@drawable/header"
    android:layout_height="260dp">
    <TextView
        android:id="@+id/text2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginStart="10dp"
        android:layout_marginTop="34dp"
        android:layout_marginBottom="10dp"
        android:text="@string/app_name"
        android:fontFamily="@font/radiocan"
        android:textColor="@color/FF5722"
        android:textSize="14sp" />

    <androidx.cardview.widget.CardView
        android:layout_width="159dp"
        android:layout_height="100dp"
        android:layout_margin="1dp"
        android:layout_below="@id/text2"
        android:layout_centerHorizontal="true"
        app:cardCornerRadius="30dp">

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@mipmap/ic_launcher" />


    </androidx.cardview.widget.CardView>




</RelativeLayout>