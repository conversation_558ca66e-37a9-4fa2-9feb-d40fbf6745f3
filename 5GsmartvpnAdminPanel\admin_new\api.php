<?php
/**
 * 5G Smart VPN Admin Panel - Modern API Endpoint
 * Serves ad configuration and server data from the new settings table
 */

require_once 'includes/config.php';

// Set JSON response header
header('Content-Type: application/json');

// HMAC request signing validation
$timestamp = $_GET['timestamp'] ?? null;
$signature = $_GET['signature'] ?? null;

if (!$timestamp || !$signature) {
    http_response_code(401);
    echo json_encode(["error" => "Unauthorized: Missing parameters"]);
    exit;
}

if (abs(time() - intval($timestamp)) > 300) { // 5 minutes window
    http_response_code(401);
    echo json_encode(["error" => "Unauthorized: Timestamp expired"]);
    exit;
}

$computedSignature = hash_hmac('sha256', $timestamp, $API_KEY);
if (!hash_equals($computedSignature, $signature)) {
    http_response_code(401);
    echo json_encode(["error" => "Unauthorized: Invalid signature"]);
    exit;
}

// Get app configuration API
if (isset($_GET['pkg'])) {
    
    try {
        // Get settings from the modern settings table
        $settings_query = "SELECT setting_key, setting_value FROM settings WHERE
            setting_key LIKE 'admob_%' OR
            setting_key LIKE 'facebook_%' OR
            setting_key LIKE '%_type' OR
            setting_key IN ('banner_enabled', 'interstitial_enabled', 'rewarded_enabled', 'native_enabled', 'openad_enabled', 'test_mode', 'click_limit', 'show_frequency', 'reward_time', 'interstitial_timing_intervals', 'max_usage_time_minutes', 'random_ad_timing_enabled')";
        
        $settings_result = mysqli_query($conn, $settings_query);
        
        $settings = [];
        if ($settings_result) {
            while ($row = mysqli_fetch_assoc($settings_result)) {
                $settings[$row['setting_key']] = $row['setting_value'];
            }
        }
        
        // Get active servers
        $server_query = "SELECT * FROM servers WHERE `status`=1 ORDER BY `pos` DESC";
        $server_result = mysqli_query($conn, $server_query);
        
        $servers_list = [];
        if ($server_result) {
            while ($row = mysqli_fetch_assoc($server_result)) {
                $servers_list[] = $row;
            }
        }
        
        // Build response data in format expected by mobile app
        $response_data = [];
        $config = [];
        
        // AdMob Configuration
        $config['admob_id'] = $settings['admob_app_id'] ?? '';
        $config['admob_banner'] = $settings['admob_banner_id'] ?? '';
        $config['admob_interstitial'] = $settings['admob_interstitial_id'] ?? '';
        $config['admob_native'] = $settings['admob_native_id'] ?? '';
        $config['admob_rewarded'] = $settings['admob_rewarded_id'] ?? '';
        $config['admob_openad'] = $settings['admob_openad_id'] ?? '';
        
        // Facebook Ads Configuration
        $config['facebook_id'] = $settings['facebook_app_id'] ?? '';
        $config['facebook_banner'] = $settings['facebook_banner_id'] ?? 'IMG_16_9_APP_INSTALL#YOUR_PLACEMENT_ID';
        $config['facebook_interstitial'] = $settings['facebook_interstitial_id'] ?? 'IMG_16_9_APP_INSTALL#YOUR_PLACEMENT_ID';
        $config['facebook_native'] = $settings['facebook_native_id'] ?? 'IMG_16_9_APP_INSTALL#YOUR_PLACEMENT_ID';
        $config['facebook_rewarded'] = $settings['facebook_rewarded_id'] ?? 'IMG_16_9_APP_INSTALL#YOUR_PLACEMENT_ID';
        
        // Ad Status and Types
        $config['ads_status'] = (
            $settings['banner_enabled'] == '1' || 
            $settings['interstitial_enabled'] == '1' || 
            $settings['rewarded_enabled'] == '1' || 
            $settings['native_enabled'] == '1' || 
            $settings['openad_enabled'] == '1'
        ) ? 1 : 0;
        
        $config['banner_type'] = $settings['banner_type'] ?? 'admob';
        $config['interstitial_type'] = $settings['interstitial_type'] ?? 'admob';
        $config['native_type'] = $settings['native_type'] ?? 'admob';
        $config['rewarded_type'] = $settings['rewarded_type'] ?? 'admob';
        
        // Additional Settings
        $config['reward_time'] = (int)($settings['reward_time'] ?? 30);
        $config['click_limit'] = (int)($settings['click_limit'] ?? 5);
        $config['show_frequency'] = (int)($settings['show_frequency'] ?? 3);
        $config['test_mode'] = (int)($settings['test_mode'] ?? 1);

        // Random Ad Timing Settings
        $config['random_ad_timing_enabled'] = (int)($settings['random_ad_timing_enabled'] ?? 1);
        $config['max_usage_time_minutes'] = (int)($settings['max_usage_time_minutes'] ?? 10);
        $config['interstitial_timing_intervals'] = $settings['interstitial_timing_intervals'] ?? '3,4,5,9';
        
        // Individual ad type enabled status
        $config['banner_enabled'] = (int)($settings['banner_enabled'] ?? 1);
        $config['interstitial_enabled'] = (int)($settings['interstitial_enabled'] ?? 1);
        $config['rewarded_enabled'] = (int)($settings['rewarded_enabled'] ?? 0);
        $config['native_enabled'] = (int)($settings['native_enabled'] ?? 0);
        $config['openad_enabled'] = (int)($settings['openad_enabled'] ?? 1);
        
        // Add servers to configuration
        $config['servers'] = $servers_list;
        
        // Add metadata
        $config['api_version'] = '3.0';
        $config['timestamp'] = time();
        $config['source'] = 'modern_admin_panel';
        
        $response_data[] = $config;
        
        // Return JSON response
        echo json_encode($response_data, JSON_PRETTY_PRINT);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            "error" => "Internal server error",
            "message" => "Failed to fetch configuration",
            "timestamp" => time()
        ]);
    }
    
} else {
    http_response_code(400);
    echo json_encode([
        "error" => "Bad request",
        "message" => "Missing required parameter: pkg",
        "timestamp" => time()
    ]);
}
?>
