<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Pressed state -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="@color/premium_gradient_start_dark"
                android:endColor="@color/premium_gradient_end_dark"
                android:angle="45" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    
    <!-- Normal state -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:startColor="@color/premium_gradient_start"
                android:endColor="@color/premium_gradient_end"
                android:angle="45" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    
</selector>
