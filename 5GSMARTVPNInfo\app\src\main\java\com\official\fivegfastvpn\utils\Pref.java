package com.official.fivegfastvpn.utils;


import static android.content.Context.MODE_PRIVATE;

import static com.official.fivegfastvpn.utils.Utils.getImgURL;

import android.content.Context;
import android.content.SharedPreferences;

import com.official.fivegfastvpn.R;
import com.official.fivegfastvpn.model.Server;

//Developer :--<PERSON><PERSON><PERSON><PERSON>
public class Pref {

    static final String APP_PREFS_NAME = "VPNPreference";

    SharedPreferences mPreference;
    SharedPreferences.Editor mPrefEditor;
    Context context;

    static final String SERVER_COUNTRY = "server_country";
    static final String SERVER_FLAG = "server_flag";
    static final String SERVER_OVPN = "server_ovpn";
    static final String SERVER_OVPN_USER = "server_ovpn_user";
    static final String SERVER_OVPN_PASSWORD = "server_ovpn_password";

    // VPN state persistence keys
    static final String VPN_CONNECTION_STATE = "vpn_connection_state";
    static final String VPN_TIMER_START_TIME = "vpn_timer_start_time";
    static final String VPN_TIMER_DURATION = "vpn_timer_duration";
    static final String VPN_IS_PREMIUM_SESSION = "vpn_is_premium_session";
    static final String VPN_REMAINING_TIME = "vpn_remaining_time";
    static final String VPN_CONNECTED_SERVER_COUNTRY = "vpn_connected_server_country";
    static final String VPN_CONNECTED_SERVER_FLAG = "vpn_connected_server_flag";


    public Pref(Context context) {
        this.mPreference = context.getSharedPreferences(APP_PREFS_NAME, MODE_PRIVATE);
        this.mPrefEditor = mPreference.edit();
        this.context = context;
    }

    public void saveServer(Server server) {
        mPrefEditor.putString(SERVER_COUNTRY, server.getCountry());
        mPrefEditor.putString(SERVER_FLAG, server.getFlagUrl());
        mPrefEditor.putString(SERVER_OVPN, server.getOvpn());
        mPrefEditor.putString(SERVER_OVPN_USER, server.getOvpnUserName());
        mPrefEditor.putString(SERVER_OVPN_PASSWORD, server.getOvpnUserPassword());
        mPrefEditor.commit();
    }

    public Server getServer() {
        return new Server(
                mPreference.getString(SERVER_COUNTRY, "Japan"),
                mPreference.getString(SERVER_FLAG, getImgURL(R.drawable.ic_japan)),
                mPreference.getString(SERVER_OVPN, "em"),
                mPreference.getString(SERVER_OVPN_USER, "vpn"),
                mPreference.getString(SERVER_OVPN_PASSWORD, "vpn")
        );
    }

    // VPN state persistence methods
    public void saveVpnConnectionState(boolean isConnected) {
        mPrefEditor.putBoolean(VPN_CONNECTION_STATE, isConnected);
        mPrefEditor.commit();
    }

    public boolean getVpnConnectionState() {
        return mPreference.getBoolean(VPN_CONNECTION_STATE, false);
    }

    public void saveVpnTimerState(long startTime, int duration, boolean isPremium, int remainingTime) {
        mPrefEditor.putLong(VPN_TIMER_START_TIME, startTime);
        mPrefEditor.putInt(VPN_TIMER_DURATION, duration);
        mPrefEditor.putBoolean(VPN_IS_PREMIUM_SESSION, isPremium);
        mPrefEditor.putInt(VPN_REMAINING_TIME, remainingTime);
        mPrefEditor.commit();
    }

    public long getVpnTimerStartTime() {
        return mPreference.getLong(VPN_TIMER_START_TIME, 0);
    }

    public int getVpnTimerDuration() {
        return mPreference.getInt(VPN_TIMER_DURATION, 0);
    }

    public boolean getVpnIsPremiumSession() {
        return mPreference.getBoolean(VPN_IS_PREMIUM_SESSION, false);
    }

    public int getVpnRemainingTime() {
        return mPreference.getInt(VPN_REMAINING_TIME, 0);
    }

    public void saveConnectedServerInfo(String country, String flagUrl) {
        mPrefEditor.putString(VPN_CONNECTED_SERVER_COUNTRY, country);
        mPrefEditor.putString(VPN_CONNECTED_SERVER_FLAG, flagUrl);
        mPrefEditor.commit();
    }

    public String getConnectedServerCountry() {
        return mPreference.getString(VPN_CONNECTED_SERVER_COUNTRY, "");
    }

    public String getConnectedServerFlag() {
        return mPreference.getString(VPN_CONNECTED_SERVER_FLAG, "");
    }

    public void clearVpnState() {
        mPrefEditor.remove(VPN_CONNECTION_STATE);
        mPrefEditor.remove(VPN_TIMER_START_TIME);
        mPrefEditor.remove(VPN_TIMER_DURATION);
        mPrefEditor.remove(VPN_IS_PREMIUM_SESSION);
        mPrefEditor.remove(VPN_REMAINING_TIME);
        mPrefEditor.remove(VPN_CONNECTED_SERVER_COUNTRY);
        mPrefEditor.remove(VPN_CONNECTED_SERVER_FLAG);
        mPrefEditor.commit();
    }

}
