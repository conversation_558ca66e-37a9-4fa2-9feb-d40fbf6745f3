// Generated by view binder compiler. Do not edit!
package com.official.fivegfastvpn.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.official.fivegfastvpn.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityNotificationsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView emptyMessage;

  @NonNull
  public final LinearLayout emptyView;

  @NonNull
  public final TextView markAllReadButton;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView recyclerViewNotifications;

  @NonNull
  public final Button retryButton;

  @NonNull
  public final SwipeRefreshLayout swipeRefreshLayout;

  @NonNull
  public final Toolbar toolbar;

  private ActivityNotificationsBinding(@NonNull LinearLayout rootView,
      @NonNull TextView emptyMessage, @NonNull LinearLayout emptyView,
      @NonNull TextView markAllReadButton, @NonNull ProgressBar progressBar,
      @NonNull RecyclerView recyclerViewNotifications, @NonNull Button retryButton,
      @NonNull SwipeRefreshLayout swipeRefreshLayout, @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.emptyMessage = emptyMessage;
    this.emptyView = emptyView;
    this.markAllReadButton = markAllReadButton;
    this.progressBar = progressBar;
    this.recyclerViewNotifications = recyclerViewNotifications;
    this.retryButton = retryButton;
    this.swipeRefreshLayout = swipeRefreshLayout;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityNotificationsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityNotificationsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_notifications, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityNotificationsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.emptyMessage;
      TextView emptyMessage = ViewBindings.findChildViewById(rootView, id);
      if (emptyMessage == null) {
        break missingId;
      }

      id = R.id.emptyView;
      LinearLayout emptyView = ViewBindings.findChildViewById(rootView, id);
      if (emptyView == null) {
        break missingId;
      }

      id = R.id.markAllReadButton;
      TextView markAllReadButton = ViewBindings.findChildViewById(rootView, id);
      if (markAllReadButton == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.recyclerViewNotifications;
      RecyclerView recyclerViewNotifications = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewNotifications == null) {
        break missingId;
      }

      id = R.id.retryButton;
      Button retryButton = ViewBindings.findChildViewById(rootView, id);
      if (retryButton == null) {
        break missingId;
      }

      id = R.id.swipeRefreshLayout;
      SwipeRefreshLayout swipeRefreshLayout = ViewBindings.findChildViewById(rootView, id);
      if (swipeRefreshLayout == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityNotificationsBinding((LinearLayout) rootView, emptyMessage, emptyView,
          markAllReadButton, progressBar, recyclerViewNotifications, retryButton,
          swipeRefreshLayout, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
