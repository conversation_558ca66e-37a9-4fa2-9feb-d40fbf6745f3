package com.official.fivegfastvpn.utils;


import static android.content.Context.MODE_PRIVATE;

import static com.official.fivegfastvpn.utils.Utils.getImgURL;

import android.content.Context;
import android.content.SharedPreferences;

import com.official.fivegfastvpn.R;
import com.official.fivegfastvpn.model.Server;

//Developer :--<PERSON><PERSON><PERSON><PERSON>
public class Pref {

    static final String APP_PREFS_NAME = "VPNPreference";

    SharedPreferences mPreference;
    SharedPreferences.Editor mPrefEditor;
    Context context;

    static final String SERVER_COUNTRY = "server_country";
    static final String SERVER_FLAG = "server_flag";
    static final String SERVER_OVPN = "server_ovpn";
    static final String SERVER_OVPN_USER = "server_ovpn_user";
    static final String SERVER_OVPN_PASSWORD = "server_ovpn_password";


    public Pref(Context context) {
        this.mPreference = context.getSharedPreferences(APP_PREFS_NAME, MODE_PRIVATE);
        this.mPrefEditor = mPreference.edit();
        this.context = context;
    }

    public void saveServer(Server server) {
        mPrefEditor.putString(SERVER_COUNTRY, server.getCountry());
        mPrefEditor.putString(SERVER_FLAG, server.getFlagUrl());
        mPrefEditor.putString(SERVER_OVPN, server.getOvpn());
        mPrefEditor.putString(SERVER_OVPN_USER, server.getOvpnUserName());
        mPrefEditor.putString(SERVER_OVPN_PASSWORD, server.getOvpnUserPassword());
        mPrefEditor.commit();
    }

    public Server getServer() {
        return new Server(
                mPreference.getString(SERVER_COUNTRY, "Japan"),
                mPreference.getString(SERVER_FLAG, getImgURL(R.drawable.ic_japan)),
                mPreference.getString(SERVER_OVPN, "em"),
                mPreference.getString(SERVER_OVPN_USER, "vpn"),
                mPreference.getString(SERVER_OVPN_PASSWORD, "vpn")
        );
    }

}
