1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.official.fivegfastvpn"
4    android:versionCode="12"
5    android:versionName="12" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:4:5-67
11-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:4:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:5:5-79
12-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:5:22-76
13    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
13-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:6:5-77
13-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:6:22-74
14    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
14-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:7:5-87
14-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:7:22-84
15    <uses-permission android:name="com.android.vending.BILLING" />
15-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:8:5-67
15-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:8:22-64
16    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
16-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:9:5-77
16-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:9:22-74
17
18    <!-- VPN Service Permissions -->
19    <uses-permission android:name="android.permission.BIND_VPN_SERVICE" />
19-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:12:5-13:47
19-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:12:22-72
20    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
20-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:14:5-78
20-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:14:22-75
21    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
21-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:15:5-81
21-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:15:22-78
22
23    <queries>
23-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:12:5-19:15
24        <intent>
24-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:13:9-15:18
25            <action android:name="com.android.vending.billing.InAppBillingService.BIND" />
25-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:14:13-91
25-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:14:21-88
26        </intent>
27        <intent>
27-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:16:9-18:18
28            <action android:name="com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND" />
28-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:17:13-116
28-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:17:21-113
29        </intent>
30
31        <package android:name="com.facebook.katana" /> <!-- For browser content -->
31-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:12:9-55
31-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:12:18-52
32        <intent>
32-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:38:9-44:18
33            <action android:name="android.intent.action.VIEW" />
33-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:13-65
33-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:21-62
34
35            <category android:name="android.intent.category.BROWSABLE" />
35-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:41:13-74
35-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:41:23-71
36
37            <data android:scheme="https" />
37-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
37-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:19-41
38        </intent> <!-- End of browser content -->
39        <!-- For CustomTabsService -->
40        <intent>
40-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:47:9-49:18
41            <action android:name="android.support.customtabs.action.CustomTabsService" />
41-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:48:13-90
41-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:48:21-87
42        </intent> <!-- End of CustomTabsService -->
43        <!-- For MRAID capabilities -->
44        <intent>
44-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:52:9-56:18
45            <action android:name="android.intent.action.INSERT" />
45-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:53:13-67
45-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:53:21-64
46
47            <data android:mimeType="vnd.android.cursor.dir/event" />
47-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
48        </intent>
49        <intent>
49-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:57:9-61:18
50            <action android:name="android.intent.action.VIEW" />
50-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:13-65
50-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:21-62
51
52            <data android:scheme="sms" />
52-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
52-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:19-41
53        </intent>
54        <intent>
54-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:62:9-66:18
55            <action android:name="android.intent.action.DIAL" />
55-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:63:13-65
55-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:63:21-62
56
57            <data android:path="tel:" />
57-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
58        </intent>
59    </queries>
60
61    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
61-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:24:5-68
61-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:24:22-65
62    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
62-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
62-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:26:22-79
63    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
63-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:17:5-79
63-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:17:22-76
64    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
64-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:27:5-82
64-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:27:22-79
65    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
65-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:28:5-88
65-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:28:22-85
66    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
66-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:29:5-83
66-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:29:22-80
67
68    <permission
68-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5335a6eacc5065e32155bfa643f759a\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
69        android:name="com.official.fivegfastvpn.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
69-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5335a6eacc5065e32155bfa643f759a\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
70        android:protectionLevel="signature" />
70-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5335a6eacc5065e32155bfa643f759a\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
71
72    <uses-permission android:name="com.official.fivegfastvpn.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
72-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5335a6eacc5065e32155bfa643f759a\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
72-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5335a6eacc5065e32155bfa643f759a\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
73
74    <application
74-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:16:5-86:15
75        android:name="com.official.fivegfastvpn.VPNApplication"
75-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:17:9-39
76        android:allowBackup="true"
76-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:18:9-35
77        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
77-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5335a6eacc5065e32155bfa643f759a\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
78        android:dataExtractionRules="@xml/data_extraction_rules"
78-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:19:9-65
79        android:debuggable="true"
80        android:extractNativeLibs="true"
81        android:fullBackupContent="@xml/backup_rules"
81-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:20:9-54
82        android:icon="@mipmap/ic_launcher"
82-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:22:9-43
83        android:label="@string/app_name"
83-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:23:9-41
84        android:networkSecurityConfig="@xml/network_security_config"
84-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:21:9-69
85        android:roundIcon="@mipmap/ic_launcher_round"
85-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:24:9-54
86        android:supportsRtl="true"
86-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:25:9-35
87        android:testOnly="true"
88        android:theme="@style/Base.Theme._5GSMARTVPNInfo"
88-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:27:9-58
89        android:usesCleartextTraffic="true" >
89-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:26:9-44
90        <activity android:name="com.official.fivegfastvpn.activity.NotificationsActivity" />
90-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:29:9-67
90-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:29:19-65
91        <activity android:name="com.official.fivegfastvpn.pro.PremiumActivity" />
91-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:30:9-57
91-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:30:19-54
92        <activity android:name="com.official.fivegfastvpn.MainActivity" />
92-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:31:9-50
92-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:31:19-47
93        <activity android:name="com.official.fivegfastvpn.activity.ServersActivity" />
93-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:32:9-62
93-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:32:19-59
94        <activity
94-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:33:9-41:20
95            android:name="com.official.fivegfastvpn.SplashActivity"
95-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:34:13-43
96            android:exported="true" >
96-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:35:13-36
97            <intent-filter>
97-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:36:13-40:29
98                <action android:name="android.intent.action.MAIN" />
98-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:37:17-69
98-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:37:25-66
99
100                <category android:name="android.intent.category.LAUNCHER" />
100-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:39:17-77
100-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:39:27-74
101            </intent-filter>
102        </activity>
103        <activity
103-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:42:9-47:52
104            android:name="de.blinkt.openvpn.DisconnectVPNActivity"
104-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:43:13-67
105            android:excludeFromRecents="true"
105-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:44:13-46
106            android:noHistory="true"
106-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:45:13-37
107            android:taskAffinity=".DisconnectVPN"
107-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:46:13-50
108            android:theme="@style/blinkt.dialog" />
108-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:47:13-49
109
110        <service
110-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:49:9-57:19
111            android:name="de.blinkt.openvpn.core.OpenVPNService"
111-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:50:13-65
112            android:exported="true"
112-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:51:13-36
113            android:foregroundServiceType="dataSync|dataSync"
113-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:52:13-53
114            android:permission="android.permission.BIND_VPN_SERVICE" >
114-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:53:13-69
115            <intent-filter>
115-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:54:13-56:29
116                <action android:name="android.net.VpnService" />
116-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:55:17-65
116-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:55:25-62
117            </intent-filter>
118        </service>
119        <service
119-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:58:9-64:19
120            android:name="com.official.fivegfastvpn.MyFirebaseMessagingService"
120-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:59:13-55
121            android:exported="false" >
121-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:60:13-37
122            <intent-filter>
122-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:61:13-63:29
123                <action android:name="com.google.firebase.MESSAGING_EVENT" />
123-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:62:17-78
123-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:62:25-75
124            </intent-filter>
125        </service>
126
127        <meta-data
127-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:66:9-68:70
128            android:name="com.google.android.gms.ads.APPLICATION_ID"
128-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:67:13-69
129            android:value="ca-app-pub-5193340328939721~2015388624" />
129-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:68:13-67
130        <meta-data
130-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:70:9-72:32
131            android:name="com.facebook.sdk.ApplicationId"
131-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:71:13-58
132            android:value="" />
132-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:72:13-29
133
134        <!-- Required: set your sentry.io project identifier (DSN) -->
135        <meta-data
135-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:75:5-159
136            android:name="io.sentry.dsn"
136-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:75:16-44
137            android:value="https://<EMAIL>/4508793236488192" />
137-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:75:45-156
138
139        <!-- enable automatic breadcrumbs for user interactions (clicks, swipes, scrolls) -->
140        <meta-data
140-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:78:5-95
141            android:name="io.sentry.traces.user-interaction.enable"
141-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:78:16-71
142            android:value="true" />
142-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:78:72-92
143        <!-- enable screenshot for crashes (could contain sensitive/PII data) -->
144        <meta-data
144-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:80:5-82
145            android:name="io.sentry.attach-screenshot"
145-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:80:16-58
146            android:value="true" />
146-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:80:59-79
147        <!-- enable view hierarchy for crashes -->
148        <meta-data
148-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:82:5-86
149            android:name="io.sentry.attach-view-hierarchy"
149-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:82:16-62
150            android:value="true" />
150-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:82:63-83
151
152        <!-- enable the performance API by setting a sample-rate, adjust in production env -->
153        <meta-data
153-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:85:5-82
154            android:name="io.sentry.traces.sample-rate"
154-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:85:16-59
155            android:value="1.0" />
155-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:85:60-79
156        <!-- VpnService-based OpenVPN implementation (V2) -->
157        <service
157-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-27:19
158            android:name="de.blinkt.openvpn.core.OpenVPNServiceV2"
158-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-67
159            android:exported="true"
159-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
160            android:foregroundServiceType="dataSync"
160-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-53
161            android:permission="android.permission.BIND_VPN_SERVICE" >
161-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-69
162            <intent-filter>
162-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:54:13-56:29
163                <action android:name="android.net.VpnService" />
163-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:55:17-65
163-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:55:25-62
164            </intent-filter>
165        </service>
166
167        <meta-data
167-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:22:9-24:37
168            android:name="com.google.android.play.billingclient.version"
168-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:23:13-73
169            android:value="7.1.1" />
169-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:24:13-34
170
171        <activity
171-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:26:9-30:75
172            android:name="com.android.billingclient.api.ProxyBillingActivity"
172-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:27:13-78
173            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
173-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:28:13-96
174            android:exported="false"
174-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:29:13-37
175            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
175-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:30:13-72
176        <activity
176-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:31:9-35:75
177            android:name="com.android.billingclient.api.ProxyBillingActivityV2"
177-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:32:13-80
178            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
178-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:33:13-96
179            android:exported="false"
179-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:34:13-37
180            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
180-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:35:13-72
181
182        <receiver
182-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
183            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
183-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
184            android:exported="true"
184-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
185            android:permission="com.google.android.c2dm.permission.SEND" >
185-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
186            <intent-filter>
186-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:33:13-35:29
187                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
187-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:34:17-81
187-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:34:25-78
188            </intent-filter>
189
190            <meta-data
190-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
191                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
191-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
192                android:value="true" />
192-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
193        </receiver>
194        <!--
195             FirebaseMessagingService performs security checks at runtime,
196             but set to not exported to explicitly avoid allowing another app to call it.
197        -->
198        <service
198-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
199            android:name="com.google.firebase.messaging.FirebaseMessagingService"
199-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
200            android:directBootAware="true"
200-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
201            android:exported="false" >
201-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
202            <intent-filter android:priority="-500" >
202-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:61:13-63:29
203                <action android:name="com.google.firebase.MESSAGING_EVENT" />
203-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:62:17-78
203-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:62:25-75
204            </intent-filter>
205        </service>
206        <service
206-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:54:9-63:19
207            android:name="com.google.firebase.components.ComponentDiscoveryService"
207-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:55:13-84
208            android:directBootAware="true"
208-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
209            android:exported="false" >
209-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:56:13-37
210            <meta-data
210-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
211                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
211-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
212                android:value="com.google.firebase.components.ComponentRegistrar" />
212-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
213            <meta-data
213-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
214                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
214-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
215                android:value="com.google.firebase.components.ComponentRegistrar" />
215-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
216            <meta-data
216-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c32355dfcf2c26b98ebc7907237c2199\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
217                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
217-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c32355dfcf2c26b98ebc7907237c2199\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
218                android:value="com.google.firebase.components.ComponentRegistrar" />
218-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c32355dfcf2c26b98ebc7907237c2199\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
219            <meta-data
219-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c32355dfcf2c26b98ebc7907237c2199\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
220                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
220-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c32355dfcf2c26b98ebc7907237c2199\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
221                android:value="com.google.firebase.components.ComponentRegistrar" />
221-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c32355dfcf2c26b98ebc7907237c2199\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
222            <meta-data
222-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63aaeacb2a36f7705d7d9290f3343c54\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
223                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
223-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63aaeacb2a36f7705d7d9290f3343c54\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
224                android:value="com.google.firebase.components.ComponentRegistrar" />
224-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63aaeacb2a36f7705d7d9290f3343c54\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
225            <meta-data
225-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
226                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
226-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
227                android:value="com.google.firebase.components.ComponentRegistrar" />
227-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
228            <meta-data
228-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f02bec30df0defe4d8c6fe403d88b9f\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
229                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
229-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f02bec30df0defe4d8c6fe403d88b9f\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
230                android:value="com.google.firebase.components.ComponentRegistrar" />
230-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f02bec30df0defe4d8c6fe403d88b9f\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
231        </service>
232
233        <activity
233-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\abafdc52b4a83dcb3e4911636b323609\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
234            android:name="com.google.android.gms.common.api.GoogleApiActivity"
234-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\abafdc52b4a83dcb3e4911636b323609\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
235            android:exported="false"
235-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\abafdc52b4a83dcb3e4911636b323609\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
236            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
236-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\abafdc52b4a83dcb3e4911636b323609\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
237        <activity
237-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:20:9-24:75
238            android:name="com.facebook.ads.AudienceNetworkActivity"
238-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:21:13-68
239            android:configChanges="keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
239-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:22:13-106
240            android:exported="false"
240-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:23:13-37
241            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
241-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:24:13-72
242
243        <provider
243-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:26:9-29:40
244            android:name="com.facebook.ads.AudienceNetworkContentProvider"
244-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:27:13-75
245            android:authorities="com.official.fivegfastvpn.AudienceNetworkContentProvider"
245-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:28:13-82
246            android:exported="false" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
246-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:29:13-37
247        <activity
247-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:73:9-78:43
248            android:name="com.google.android.gms.ads.AdActivity"
248-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:74:13-65
249            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
249-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:75:13-122
250            android:exported="false"
250-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:76:13-37
251            android:theme="@android:style/Theme.Translucent" />
251-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:77:13-61
252
253        <provider
253-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:80:9-85:43
254            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
254-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:81:13-76
255            android:authorities="com.official.fivegfastvpn.mobileadsinitprovider"
255-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:82:13-73
256            android:exported="false"
256-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:83:13-37
257            android:initOrder="100" />
257-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:84:13-36
258
259        <service
259-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:87:9-91:43
260            android:name="com.google.android.gms.ads.AdService"
260-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:88:13-64
261            android:enabled="true"
261-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:89:13-35
262            android:exported="false" />
262-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:90:13-37
263
264        <activity
264-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:93:9-97:43
265            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
265-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:94:13-82
266            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
266-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:95:13-122
267            android:exported="false" />
267-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:96:13-37
268        <activity
268-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:98:9-105:43
269            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
269-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:99:13-82
270            android:excludeFromRecents="true"
270-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:100:13-46
271            android:exported="false"
271-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:101:13-37
272            android:launchMode="singleTask"
272-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:102:13-44
273            android:taskAffinity=""
273-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:103:13-36
274            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
274-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:104:13-72
275
276        <meta-data
276-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:107:9-109:36
277            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
277-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:108:13-79
278            android:value="true" />
278-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:109:13-33
279        <meta-data
279-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:110:9-112:36
280            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
280-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:111:13-83
281            android:value="true" />
281-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:112:13-33
282
283        <provider
283-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
284            android:name="com.google.firebase.provider.FirebaseInitProvider"
284-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
285            android:authorities="com.official.fivegfastvpn.firebaseinitprovider"
285-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
286            android:directBootAware="true"
286-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
287            android:exported="false"
287-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
288            android:initOrder="100" />
288-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
289        <provider
289-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
290            android:name="androidx.startup.InitializationProvider"
290-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:30:13-67
291            android:authorities="com.official.fivegfastvpn.androidx-startup"
291-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:31:13-68
292            android:exported="false" >
292-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:32:13-37
293            <meta-data
293-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:34:13-36:52
294                android:name="androidx.work.WorkManagerInitializer"
294-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:35:17-68
295                android:value="androidx.startup" />
295-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:36:17-49
296            <meta-data
296-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c1868e2fbf1ecc0125468b7c6a9b846\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
297                android:name="androidx.emoji2.text.EmojiCompatInitializer"
297-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c1868e2fbf1ecc0125468b7c6a9b846\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
298                android:value="androidx.startup" />
298-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c1868e2fbf1ecc0125468b7c6a9b846\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
299            <meta-data
299-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cc925df91b3cd356b1aebb6c5f064f8\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
300                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
300-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cc925df91b3cd356b1aebb6c5f064f8\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
301                android:value="androidx.startup" />
301-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cc925df91b3cd356b1aebb6c5f064f8\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
302            <meta-data
302-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
303                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
303-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
304                android:value="androidx.startup" />
304-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
305        </provider>
306
307        <service
307-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
308            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
308-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
309            android:directBootAware="false"
309-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
310            android:enabled="@bool/enable_system_alarm_service_default"
310-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
311            android:exported="false" />
311-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
312        <service
312-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
313            android:name="androidx.work.impl.background.systemjob.SystemJobService"
313-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
314            android:directBootAware="false"
314-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
315            android:enabled="@bool/enable_system_job_service_default"
315-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
316            android:exported="true"
316-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
317            android:permission="android.permission.BIND_JOB_SERVICE" />
317-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
318        <service
318-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
319            android:name="androidx.work.impl.foreground.SystemForegroundService"
319-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
320            android:directBootAware="false"
320-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
321            android:enabled="@bool/enable_system_foreground_service_default"
321-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
322            android:exported="false" />
322-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
323
324        <receiver
324-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
325            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
325-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
326            android:directBootAware="false"
326-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
327            android:enabled="true"
327-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
328            android:exported="false" />
328-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
329        <receiver
329-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
330            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
330-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
331            android:directBootAware="false"
331-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
332            android:enabled="false"
332-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
333            android:exported="false" >
333-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
334            <intent-filter>
334-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
335                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
335-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
335-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
336                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
336-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
336-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
337            </intent-filter>
338        </receiver>
339        <receiver
339-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
340            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
340-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
341            android:directBootAware="false"
341-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
342            android:enabled="false"
342-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
343            android:exported="false" >
343-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
344            <intent-filter>
344-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
345                <action android:name="android.intent.action.BATTERY_OKAY" />
345-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
345-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
346                <action android:name="android.intent.action.BATTERY_LOW" />
346-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
346-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
347            </intent-filter>
348        </receiver>
349        <receiver
349-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
350            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
350-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
351            android:directBootAware="false"
351-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
352            android:enabled="false"
352-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
353            android:exported="false" >
353-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
354            <intent-filter>
354-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
355                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
355-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
355-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
356                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
356-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
356-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
357            </intent-filter>
358        </receiver>
359        <receiver
359-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
360            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
360-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
361            android:directBootAware="false"
361-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
362            android:enabled="false"
362-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
363            android:exported="false" >
363-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
364            <intent-filter>
364-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
365                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
365-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
365-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
366            </intent-filter>
367        </receiver>
368        <receiver
368-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
369            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
369-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
370            android:directBootAware="false"
370-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
371            android:enabled="false"
371-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
372            android:exported="false" >
372-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
373            <intent-filter>
373-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
374                <action android:name="android.intent.action.BOOT_COMPLETED" />
374-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:17-79
374-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:25-76
375                <action android:name="android.intent.action.TIME_SET" />
375-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
375-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
376                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
376-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
376-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
377            </intent-filter>
378        </receiver>
379        <receiver
379-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
380            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
380-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
381            android:directBootAware="false"
381-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
382            android:enabled="@bool/enable_system_alarm_service_default"
382-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
383            android:exported="false" >
383-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
384            <intent-filter>
384-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
385                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
385-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
385-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
386            </intent-filter>
387        </receiver>
388        <receiver
388-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
389            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
389-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
390            android:directBootAware="false"
390-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
391            android:enabled="true"
391-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
392            android:exported="true"
392-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
393            android:permission="android.permission.DUMP" >
393-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
394            <intent-filter>
394-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
395                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
395-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
395-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
396            </intent-filter>
397        </receiver>
398
399        <service
399-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a88bdec8a912efc00babe52f942fa09a\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
400            android:name="androidx.room.MultiInstanceInvalidationService"
400-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a88bdec8a912efc00babe52f942fa09a\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
401            android:directBootAware="true"
401-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a88bdec8a912efc00babe52f942fa09a\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
402            android:exported="false" />
402-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a88bdec8a912efc00babe52f942fa09a\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
403
404        <uses-library
404-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cf4f5caab9a5df674a7f293958b043f\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
405            android:name="androidx.window.extensions"
405-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cf4f5caab9a5df674a7f293958b043f\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
406            android:required="false" />
406-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cf4f5caab9a5df674a7f293958b043f\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
407        <uses-library
407-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cf4f5caab9a5df674a7f293958b043f\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
408            android:name="androidx.window.sidecar"
408-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cf4f5caab9a5df674a7f293958b043f\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
409            android:required="false" /> <!-- 'android:authorities' must be unique in the device, across all apps -->
409-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cf4f5caab9a5df674a7f293958b043f\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
410        <provider
410-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:12:9-15:40
411            android:name="io.sentry.android.core.SentryInitProvider"
411-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:13:13-69
412            android:authorities="com.official.fivegfastvpn.SentryInitProvider"
412-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:14:13-70
413            android:exported="false" />
413-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:15:13-37
414        <provider
414-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:16:9-20:39
415            android:name="io.sentry.android.core.SentryPerformanceProvider"
415-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:17:13-76
416            android:authorities="com.official.fivegfastvpn.SentryPerformanceProvider"
416-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:18:13-77
417            android:exported="false"
417-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:19:13-37
418            android:initOrder="200" />
418-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:20:13-36
419
420        <uses-library
420-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04acc53f658e9c1c6ef85818b36fbc26\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
421            android:name="android.ext.adservices"
421-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04acc53f658e9c1c6ef85818b36fbc26\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
422            android:required="false" />
422-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04acc53f658e9c1c6ef85818b36fbc26\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
423
424        <meta-data
424-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1aae7574d5734885cc339c3642ba94ef\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
425            android:name="com.google.android.gms.version"
425-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1aae7574d5734885cc339c3642ba94ef\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
426            android:value="@integer/google_play_services_version" />
426-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1aae7574d5734885cc339c3642ba94ef\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
427
428        <receiver
428-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
429            android:name="androidx.profileinstaller.ProfileInstallReceiver"
429-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
430            android:directBootAware="false"
430-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
431            android:enabled="true"
431-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
432            android:exported="true"
432-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
433            android:permission="android.permission.DUMP" >
433-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
434            <intent-filter>
434-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
435                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
435-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
435-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
436            </intent-filter>
437            <intent-filter>
437-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
438                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
438-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
438-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
439            </intent-filter>
440            <intent-filter>
440-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
441                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
441-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
441-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
442            </intent-filter>
443            <intent-filter>
443-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
444                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
444-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
444-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
445            </intent-filter>
446        </receiver>
447
448        <service
448-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f52e0b63325323d499cb62a020bb0cf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
449            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
449-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f52e0b63325323d499cb62a020bb0cf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
450            android:exported="false" >
450-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f52e0b63325323d499cb62a020bb0cf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
451            <meta-data
451-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f52e0b63325323d499cb62a020bb0cf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
452                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
452-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f52e0b63325323d499cb62a020bb0cf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
453                android:value="cct" />
453-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f52e0b63325323d499cb62a020bb0cf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
454        </service>
455        <service
455-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41184f8ba20d1afc615f0577f56d25b7\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
456            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
456-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41184f8ba20d1afc615f0577f56d25b7\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
457            android:exported="false"
457-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41184f8ba20d1afc615f0577f56d25b7\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
458            android:permission="android.permission.BIND_JOB_SERVICE" >
458-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41184f8ba20d1afc615f0577f56d25b7\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
459        </service>
460
461        <receiver
461-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41184f8ba20d1afc615f0577f56d25b7\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
462            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
462-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41184f8ba20d1afc615f0577f56d25b7\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
463            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
463-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41184f8ba20d1afc615f0577f56d25b7\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
464        <activity
464-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e98fd6bc8fdf57163ff3533ceec6a5b\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
465            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
465-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e98fd6bc8fdf57163ff3533ceec6a5b\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
466            android:exported="false"
466-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e98fd6bc8fdf57163ff3533ceec6a5b\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
467            android:stateNotNeeded="true"
467-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e98fd6bc8fdf57163ff3533ceec6a5b\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
468            android:theme="@style/Theme.PlayCore.Transparent" />
468-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e98fd6bc8fdf57163ff3533ceec6a5b\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
469    </application>
470
471</manifest>
