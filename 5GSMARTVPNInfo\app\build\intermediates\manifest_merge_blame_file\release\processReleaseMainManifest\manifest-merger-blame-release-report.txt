1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.official.fivegfastvpn"
4    android:versionCode="12"
5    android:versionName="12" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:4:5-67
11-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:4:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:5:5-79
12-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:5:22-76
13    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
13-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:6:5-77
13-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:6:22-74
14    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
14-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:7:5-87
14-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:7:22-84
15    <uses-permission android:name="com.android.vending.BILLING" />
15-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:8:5-67
15-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:8:22-64
16    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
16-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:9:5-77
16-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:9:22-74
17
18    <!-- VPN Service Permissions -->
19    <uses-permission android:name="android.permission.BIND_VPN_SERVICE" />
19-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:12:5-13:47
19-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:12:22-72
20    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
20-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:14:5-78
20-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:14:22-75
21    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
21-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:15:5-81
21-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:15:22-78
22
23    <queries>
23-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:12:5-19:15
24        <intent>
24-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:13:9-15:18
25            <action android:name="com.android.vending.billing.InAppBillingService.BIND" />
25-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:14:13-91
25-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:14:21-88
26        </intent>
27        <intent>
27-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:16:9-18:18
28            <action android:name="com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND" />
28-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:17:13-116
28-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:17:21-113
29        </intent>
30
31        <package android:name="com.facebook.katana" /> <!-- For browser content -->
31-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:12:9-55
31-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:12:18-52
32        <intent>
32-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:38:9-44:18
33            <action android:name="android.intent.action.VIEW" />
33-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:13-65
33-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:21-62
34
35            <category android:name="android.intent.category.BROWSABLE" />
35-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:41:13-74
35-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:41:23-71
36
37            <data android:scheme="https" />
37-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
37-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:19-41
38        </intent> <!-- End of browser content -->
39        <!-- For CustomTabsService -->
40        <intent>
40-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:47:9-49:18
41            <action android:name="android.support.customtabs.action.CustomTabsService" />
41-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:48:13-90
41-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:48:21-87
42        </intent> <!-- End of CustomTabsService -->
43        <!-- For MRAID capabilities -->
44        <intent>
44-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:52:9-56:18
45            <action android:name="android.intent.action.INSERT" />
45-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:53:13-67
45-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:53:21-64
46
47            <data android:mimeType="vnd.android.cursor.dir/event" />
47-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
48        </intent>
49        <intent>
49-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:57:9-61:18
50            <action android:name="android.intent.action.VIEW" />
50-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:13-65
50-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:21-62
51
52            <data android:scheme="sms" />
52-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
52-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:19-41
53        </intent>
54        <intent>
54-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:62:9-66:18
55            <action android:name="android.intent.action.DIAL" />
55-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:63:13-65
55-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:63:21-62
56
57            <data android:path="tel:" />
57-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
58        </intent>
59    </queries>
60
61    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
61-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:24:5-68
61-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:24:22-65
62    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
62-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
62-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:26:22-79
63    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
63-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:17:5-79
63-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:17:22-76
64    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
64-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:27:5-82
64-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:27:22-79
65    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
65-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:28:5-88
65-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:28:22-85
66    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
66-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:29:5-83
66-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:29:22-80
67
68    <permission
68-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5335a6eacc5065e32155bfa643f759a\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
69        android:name="com.official.fivegfastvpn.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
69-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5335a6eacc5065e32155bfa643f759a\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
70        android:protectionLevel="signature" />
70-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5335a6eacc5065e32155bfa643f759a\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
71
72    <uses-permission android:name="com.official.fivegfastvpn.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
72-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5335a6eacc5065e32155bfa643f759a\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
72-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5335a6eacc5065e32155bfa643f759a\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
73
74    <application
74-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:16:5-86:15
75        android:name="com.official.fivegfastvpn.VPNApplication"
75-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:17:9-39
76        android:allowBackup="true"
76-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:18:9-35
77        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
77-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5335a6eacc5065e32155bfa643f759a\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
78        android:dataExtractionRules="@xml/data_extraction_rules"
78-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:19:9-65
79        android:extractNativeLibs="true"
80        android:fullBackupContent="@xml/backup_rules"
80-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:20:9-54
81        android:icon="@mipmap/ic_launcher"
81-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:22:9-43
82        android:label="@string/app_name"
82-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:23:9-41
83        android:networkSecurityConfig="@xml/network_security_config"
83-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:21:9-69
84        android:roundIcon="@mipmap/ic_launcher_round"
84-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:24:9-54
85        android:supportsRtl="true"
85-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:25:9-35
86        android:theme="@style/Base.Theme._5GSMARTVPNInfo"
86-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:27:9-58
87        android:usesCleartextTraffic="true" >
87-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:26:9-44
88        <activity android:name="com.official.fivegfastvpn.activity.NotificationsActivity" />
88-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:29:9-67
88-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:29:19-65
89        <activity android:name="com.official.fivegfastvpn.pro.PremiumActivity" />
89-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:30:9-57
89-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:30:19-54
90        <activity android:name="com.official.fivegfastvpn.MainActivity" />
90-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:31:9-50
90-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:31:19-47
91        <activity android:name="com.official.fivegfastvpn.activity.ServersActivity" />
91-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:32:9-62
91-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:32:19-59
92        <activity
92-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:33:9-41:20
93            android:name="com.official.fivegfastvpn.SplashActivity"
93-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:34:13-43
94            android:exported="true" >
94-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:35:13-36
95            <intent-filter>
95-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:36:13-40:29
96                <action android:name="android.intent.action.MAIN" />
96-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:37:17-69
96-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:37:25-66
97
98                <category android:name="android.intent.category.LAUNCHER" />
98-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:39:17-77
98-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:39:27-74
99            </intent-filter>
100        </activity>
101        <activity
101-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:42:9-47:52
102            android:name="de.blinkt.openvpn.DisconnectVPNActivity"
102-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:43:13-67
103            android:excludeFromRecents="true"
103-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:44:13-46
104            android:noHistory="true"
104-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:45:13-37
105            android:taskAffinity=".DisconnectVPN"
105-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:46:13-50
106            android:theme="@style/blinkt.dialog" />
106-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:47:13-49
107
108        <service
108-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:49:9-57:19
109            android:name="de.blinkt.openvpn.core.OpenVPNService"
109-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:50:13-65
110            android:exported="true"
110-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:51:13-36
111            android:foregroundServiceType="dataSync|dataSync"
111-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:52:13-53
112            android:permission="android.permission.BIND_VPN_SERVICE" >
112-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:53:13-69
113            <intent-filter>
113-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:54:13-56:29
114                <action android:name="android.net.VpnService" />
114-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:55:17-65
114-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:55:25-62
115            </intent-filter>
116        </service>
117        <service
117-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:58:9-64:19
118            android:name="com.official.fivegfastvpn.MyFirebaseMessagingService"
118-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:59:13-55
119            android:exported="false" >
119-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:60:13-37
120            <intent-filter>
120-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:61:13-63:29
121                <action android:name="com.google.firebase.MESSAGING_EVENT" />
121-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:62:17-78
121-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:62:25-75
122            </intent-filter>
123        </service>
124
125        <meta-data
125-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:66:9-68:70
126            android:name="com.google.android.gms.ads.APPLICATION_ID"
126-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:67:13-69
127            android:value="ca-app-pub-5193340328939721~2015388624" />
127-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:68:13-67
128        <meta-data
128-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:70:9-72:32
129            android:name="com.facebook.sdk.ApplicationId"
129-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:71:13-58
130            android:value="" />
130-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:72:13-29
131
132        <!-- Required: set your sentry.io project identifier (DSN) -->
133        <meta-data
133-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:75:5-159
134            android:name="io.sentry.dsn"
134-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:75:16-44
135            android:value="https://<EMAIL>/4508793236488192" />
135-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:75:45-156
136
137        <!-- enable automatic breadcrumbs for user interactions (clicks, swipes, scrolls) -->
138        <meta-data
138-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:78:5-95
139            android:name="io.sentry.traces.user-interaction.enable"
139-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:78:16-71
140            android:value="true" />
140-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:78:72-92
141        <!-- enable screenshot for crashes (could contain sensitive/PII data) -->
142        <meta-data
142-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:80:5-82
143            android:name="io.sentry.attach-screenshot"
143-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:80:16-58
144            android:value="true" />
144-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:80:59-79
145        <!-- enable view hierarchy for crashes -->
146        <meta-data
146-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:82:5-86
147            android:name="io.sentry.attach-view-hierarchy"
147-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:82:16-62
148            android:value="true" />
148-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:82:63-83
149
150        <!-- enable the performance API by setting a sample-rate, adjust in production env -->
151        <meta-data
151-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:85:5-82
152            android:name="io.sentry.traces.sample-rate"
152-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:85:16-59
153            android:value="1.0" />
153-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:85:60-79
154        <!-- VpnService-based OpenVPN implementation (V2) -->
155        <service
155-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:9-27:19
156            android:name="de.blinkt.openvpn.core.OpenVPNServiceV2"
156-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-67
157            android:exported="true"
157-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-36
158            android:foregroundServiceType="dataSync"
158-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-53
159            android:permission="android.permission.BIND_VPN_SERVICE" >
159-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-69
160            <intent-filter>
160-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:54:13-56:29
161                <action android:name="android.net.VpnService" />
161-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:55:17-65
161-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:55:25-62
162            </intent-filter>
163        </service>
164
165        <meta-data
165-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:22:9-24:37
166            android:name="com.google.android.play.billingclient.version"
166-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:23:13-73
167            android:value="7.1.1" />
167-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:24:13-34
168
169        <activity
169-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:26:9-30:75
170            android:name="com.android.billingclient.api.ProxyBillingActivity"
170-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:27:13-78
171            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
171-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:28:13-96
172            android:exported="false"
172-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:29:13-37
173            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
173-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:30:13-72
174        <activity
174-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:31:9-35:75
175            android:name="com.android.billingclient.api.ProxyBillingActivityV2"
175-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:32:13-80
176            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
176-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:33:13-96
177            android:exported="false"
177-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:34:13-37
178            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
178-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:35:13-72
179
180        <receiver
180-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
181            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
181-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
182            android:exported="true"
182-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
183            android:permission="com.google.android.c2dm.permission.SEND" >
183-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
184            <intent-filter>
184-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:33:13-35:29
185                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
185-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:34:17-81
185-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:34:25-78
186            </intent-filter>
187
188            <meta-data
188-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
189                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
189-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
190                android:value="true" />
190-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
191        </receiver>
192        <!--
193             FirebaseMessagingService performs security checks at runtime,
194             but set to not exported to explicitly avoid allowing another app to call it.
195        -->
196        <service
196-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
197            android:name="com.google.firebase.messaging.FirebaseMessagingService"
197-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
198            android:directBootAware="true"
198-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
199            android:exported="false" >
199-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
200            <intent-filter android:priority="-500" >
200-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:61:13-63:29
201                <action android:name="com.google.firebase.MESSAGING_EVENT" />
201-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:62:17-78
201-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:62:25-75
202            </intent-filter>
203        </service>
204        <service
204-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:54:9-63:19
205            android:name="com.google.firebase.components.ComponentDiscoveryService"
205-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:55:13-84
206            android:directBootAware="true"
206-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
207            android:exported="false" >
207-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:56:13-37
208            <meta-data
208-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
209                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
209-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
210                android:value="com.google.firebase.components.ComponentRegistrar" />
210-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
211            <meta-data
211-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
212                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
212-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
213                android:value="com.google.firebase.components.ComponentRegistrar" />
213-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
214            <meta-data
214-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c32355dfcf2c26b98ebc7907237c2199\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
215                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
215-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c32355dfcf2c26b98ebc7907237c2199\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
216                android:value="com.google.firebase.components.ComponentRegistrar" />
216-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c32355dfcf2c26b98ebc7907237c2199\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
217            <meta-data
217-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c32355dfcf2c26b98ebc7907237c2199\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
218                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
218-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c32355dfcf2c26b98ebc7907237c2199\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
219                android:value="com.google.firebase.components.ComponentRegistrar" />
219-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c32355dfcf2c26b98ebc7907237c2199\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
220            <meta-data
220-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63aaeacb2a36f7705d7d9290f3343c54\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
221                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
221-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63aaeacb2a36f7705d7d9290f3343c54\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
222                android:value="com.google.firebase.components.ComponentRegistrar" />
222-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63aaeacb2a36f7705d7d9290f3343c54\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
223            <meta-data
223-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
224                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
224-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
225                android:value="com.google.firebase.components.ComponentRegistrar" />
225-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
226            <meta-data
226-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f02bec30df0defe4d8c6fe403d88b9f\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
227                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
227-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f02bec30df0defe4d8c6fe403d88b9f\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
228                android:value="com.google.firebase.components.ComponentRegistrar" />
228-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f02bec30df0defe4d8c6fe403d88b9f\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
229        </service>
230
231        <activity
231-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\abafdc52b4a83dcb3e4911636b323609\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
232            android:name="com.google.android.gms.common.api.GoogleApiActivity"
232-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\abafdc52b4a83dcb3e4911636b323609\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
233            android:exported="false"
233-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\abafdc52b4a83dcb3e4911636b323609\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
234            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
234-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\abafdc52b4a83dcb3e4911636b323609\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
235        <activity
235-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:20:9-24:75
236            android:name="com.facebook.ads.AudienceNetworkActivity"
236-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:21:13-68
237            android:configChanges="keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
237-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:22:13-106
238            android:exported="false"
238-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:23:13-37
239            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
239-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:24:13-72
240
241        <provider
241-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:26:9-29:40
242            android:name="com.facebook.ads.AudienceNetworkContentProvider"
242-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:27:13-75
243            android:authorities="com.official.fivegfastvpn.AudienceNetworkContentProvider"
243-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:28:13-82
244            android:exported="false" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
244-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:29:13-37
245        <activity
245-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:73:9-78:43
246            android:name="com.google.android.gms.ads.AdActivity"
246-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:74:13-65
247            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
247-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:75:13-122
248            android:exported="false"
248-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:76:13-37
249            android:theme="@android:style/Theme.Translucent" />
249-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:77:13-61
250
251        <provider
251-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:80:9-85:43
252            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
252-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:81:13-76
253            android:authorities="com.official.fivegfastvpn.mobileadsinitprovider"
253-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:82:13-73
254            android:exported="false"
254-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:83:13-37
255            android:initOrder="100" />
255-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:84:13-36
256
257        <service
257-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:87:9-91:43
258            android:name="com.google.android.gms.ads.AdService"
258-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:88:13-64
259            android:enabled="true"
259-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:89:13-35
260            android:exported="false" />
260-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:90:13-37
261
262        <activity
262-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:93:9-97:43
263            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
263-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:94:13-82
264            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
264-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:95:13-122
265            android:exported="false" />
265-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:96:13-37
266        <activity
266-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:98:9-105:43
267            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
267-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:99:13-82
268            android:excludeFromRecents="true"
268-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:100:13-46
269            android:exported="false"
269-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:101:13-37
270            android:launchMode="singleTask"
270-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:102:13-44
271            android:taskAffinity=""
271-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:103:13-36
272            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
272-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:104:13-72
273
274        <meta-data
274-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:107:9-109:36
275            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
275-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:108:13-79
276            android:value="true" />
276-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:109:13-33
277        <meta-data
277-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:110:9-112:36
278            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
278-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:111:13-83
279            android:value="true" />
279-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:112:13-33
280
281        <provider
281-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
282            android:name="com.google.firebase.provider.FirebaseInitProvider"
282-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
283            android:authorities="com.official.fivegfastvpn.firebaseinitprovider"
283-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
284            android:directBootAware="true"
284-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
285            android:exported="false"
285-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
286            android:initOrder="100" />
286-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
287        <provider
287-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
288            android:name="androidx.startup.InitializationProvider"
288-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:30:13-67
289            android:authorities="com.official.fivegfastvpn.androidx-startup"
289-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:31:13-68
290            android:exported="false" >
290-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:32:13-37
291            <meta-data
291-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:34:13-36:52
292                android:name="androidx.work.WorkManagerInitializer"
292-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:35:17-68
293                android:value="androidx.startup" />
293-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:36:17-49
294            <meta-data
294-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c1868e2fbf1ecc0125468b7c6a9b846\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
295                android:name="androidx.emoji2.text.EmojiCompatInitializer"
295-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c1868e2fbf1ecc0125468b7c6a9b846\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
296                android:value="androidx.startup" />
296-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c1868e2fbf1ecc0125468b7c6a9b846\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
297            <meta-data
297-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cc925df91b3cd356b1aebb6c5f064f8\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
298                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
298-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cc925df91b3cd356b1aebb6c5f064f8\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
299                android:value="androidx.startup" />
299-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cc925df91b3cd356b1aebb6c5f064f8\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
300            <meta-data
300-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
301                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
301-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
302                android:value="androidx.startup" />
302-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
303        </provider>
304
305        <service
305-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
306            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
306-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
307            android:directBootAware="false"
307-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
308            android:enabled="@bool/enable_system_alarm_service_default"
308-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
309            android:exported="false" />
309-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
310        <service
310-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
311            android:name="androidx.work.impl.background.systemjob.SystemJobService"
311-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
312            android:directBootAware="false"
312-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
313            android:enabled="@bool/enable_system_job_service_default"
313-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
314            android:exported="true"
314-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
315            android:permission="android.permission.BIND_JOB_SERVICE" />
315-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
316        <service
316-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
317            android:name="androidx.work.impl.foreground.SystemForegroundService"
317-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
318            android:directBootAware="false"
318-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
319            android:enabled="@bool/enable_system_foreground_service_default"
319-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
320            android:exported="false" />
320-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
321
322        <receiver
322-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
323            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
323-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
324            android:directBootAware="false"
324-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
325            android:enabled="true"
325-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
326            android:exported="false" />
326-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
327        <receiver
327-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
328            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
328-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
329            android:directBootAware="false"
329-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
330            android:enabled="false"
330-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
331            android:exported="false" >
331-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
332            <intent-filter>
332-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
333                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
333-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
333-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
334                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
334-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
334-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
335            </intent-filter>
336        </receiver>
337        <receiver
337-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
338            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
338-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
339            android:directBootAware="false"
339-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
340            android:enabled="false"
340-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
341            android:exported="false" >
341-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
342            <intent-filter>
342-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
343                <action android:name="android.intent.action.BATTERY_OKAY" />
343-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
343-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
344                <action android:name="android.intent.action.BATTERY_LOW" />
344-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
344-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
345            </intent-filter>
346        </receiver>
347        <receiver
347-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
348            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
348-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
349            android:directBootAware="false"
349-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
350            android:enabled="false"
350-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
351            android:exported="false" >
351-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
352            <intent-filter>
352-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
353                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
353-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
353-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
354                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
354-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
354-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
355            </intent-filter>
356        </receiver>
357        <receiver
357-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
358            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
358-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
359            android:directBootAware="false"
359-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
360            android:enabled="false"
360-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
361            android:exported="false" >
361-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
362            <intent-filter>
362-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
363                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
363-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
363-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
364            </intent-filter>
365        </receiver>
366        <receiver
366-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
367            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
367-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
368            android:directBootAware="false"
368-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
369            android:enabled="false"
369-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
370            android:exported="false" >
370-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
371            <intent-filter>
371-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
372                <action android:name="android.intent.action.BOOT_COMPLETED" />
372-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:17-79
372-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:25-76
373                <action android:name="android.intent.action.TIME_SET" />
373-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
373-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
374                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
374-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
374-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
375            </intent-filter>
376        </receiver>
377        <receiver
377-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
378            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
378-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
379            android:directBootAware="false"
379-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
380            android:enabled="@bool/enable_system_alarm_service_default"
380-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
381            android:exported="false" >
381-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
382            <intent-filter>
382-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
383                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
383-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
383-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
384            </intent-filter>
385        </receiver>
386        <receiver
386-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
387            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
387-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
388            android:directBootAware="false"
388-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
389            android:enabled="true"
389-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
390            android:exported="true"
390-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
391            android:permission="android.permission.DUMP" >
391-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
392            <intent-filter>
392-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
393                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
393-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
393-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
394            </intent-filter>
395        </receiver>
396
397        <service
397-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a88bdec8a912efc00babe52f942fa09a\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
398            android:name="androidx.room.MultiInstanceInvalidationService"
398-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a88bdec8a912efc00babe52f942fa09a\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
399            android:directBootAware="true"
399-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a88bdec8a912efc00babe52f942fa09a\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
400            android:exported="false" />
400-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a88bdec8a912efc00babe52f942fa09a\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
401
402        <uses-library
402-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cf4f5caab9a5df674a7f293958b043f\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
403            android:name="androidx.window.extensions"
403-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cf4f5caab9a5df674a7f293958b043f\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
404            android:required="false" />
404-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cf4f5caab9a5df674a7f293958b043f\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
405        <uses-library
405-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cf4f5caab9a5df674a7f293958b043f\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
406            android:name="androidx.window.sidecar"
406-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cf4f5caab9a5df674a7f293958b043f\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
407            android:required="false" /> <!-- 'android:authorities' must be unique in the device, across all apps -->
407-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cf4f5caab9a5df674a7f293958b043f\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
408        <provider
408-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:12:9-15:40
409            android:name="io.sentry.android.core.SentryInitProvider"
409-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:13:13-69
410            android:authorities="com.official.fivegfastvpn.SentryInitProvider"
410-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:14:13-70
411            android:exported="false" />
411-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:15:13-37
412        <provider
412-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:16:9-20:39
413            android:name="io.sentry.android.core.SentryPerformanceProvider"
413-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:17:13-76
414            android:authorities="com.official.fivegfastvpn.SentryPerformanceProvider"
414-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:18:13-77
415            android:exported="false"
415-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:19:13-37
416            android:initOrder="200" />
416-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:20:13-36
417
418        <uses-library
418-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04acc53f658e9c1c6ef85818b36fbc26\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
419            android:name="android.ext.adservices"
419-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04acc53f658e9c1c6ef85818b36fbc26\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
420            android:required="false" />
420-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04acc53f658e9c1c6ef85818b36fbc26\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
421
422        <meta-data
422-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1aae7574d5734885cc339c3642ba94ef\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
423            android:name="com.google.android.gms.version"
423-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1aae7574d5734885cc339c3642ba94ef\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
424            android:value="@integer/google_play_services_version" />
424-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1aae7574d5734885cc339c3642ba94ef\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
425
426        <receiver
426-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
427            android:name="androidx.profileinstaller.ProfileInstallReceiver"
427-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
428            android:directBootAware="false"
428-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
429            android:enabled="true"
429-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
430            android:exported="true"
430-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
431            android:permission="android.permission.DUMP" >
431-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
432            <intent-filter>
432-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
433                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
433-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
433-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
434            </intent-filter>
435            <intent-filter>
435-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
436                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
436-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
436-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
437            </intent-filter>
438            <intent-filter>
438-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
439                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
439-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
439-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
440            </intent-filter>
441            <intent-filter>
441-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
442                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
442-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
442-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
443            </intent-filter>
444        </receiver>
445
446        <service
446-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f52e0b63325323d499cb62a020bb0cf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
447            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
447-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f52e0b63325323d499cb62a020bb0cf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
448            android:exported="false" >
448-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f52e0b63325323d499cb62a020bb0cf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
449            <meta-data
449-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f52e0b63325323d499cb62a020bb0cf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
450                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
450-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f52e0b63325323d499cb62a020bb0cf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
451                android:value="cct" />
451-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f52e0b63325323d499cb62a020bb0cf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
452        </service>
453        <service
453-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41184f8ba20d1afc615f0577f56d25b7\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
454            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
454-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41184f8ba20d1afc615f0577f56d25b7\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
455            android:exported="false"
455-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41184f8ba20d1afc615f0577f56d25b7\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
456            android:permission="android.permission.BIND_JOB_SERVICE" >
456-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41184f8ba20d1afc615f0577f56d25b7\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
457        </service>
458
459        <receiver
459-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41184f8ba20d1afc615f0577f56d25b7\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
460            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
460-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41184f8ba20d1afc615f0577f56d25b7\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
461            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
461-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41184f8ba20d1afc615f0577f56d25b7\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
462        <activity
462-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e98fd6bc8fdf57163ff3533ceec6a5b\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
463            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
463-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e98fd6bc8fdf57163ff3533ceec6a5b\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
464            android:exported="false"
464-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e98fd6bc8fdf57163ff3533ceec6a5b\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
465            android:stateNotNeeded="true"
465-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e98fd6bc8fdf57163ff3533ceec6a5b\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
466            android:theme="@style/Theme.PlayCore.Transparent" />
466-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e98fd6bc8fdf57163ff3533ceec6a5b\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
467    </application>
468
469</manifest>
