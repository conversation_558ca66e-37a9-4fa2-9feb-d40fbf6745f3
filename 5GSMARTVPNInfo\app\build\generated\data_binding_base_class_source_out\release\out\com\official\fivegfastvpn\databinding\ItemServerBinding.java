// Generated by view binder compiler. Do not edit!
package com.official.fivegfastvpn.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.official.fivegfastvpn.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemServerBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final ImageView btnSelected;

  @NonNull
  public final TextView countryName;

  @NonNull
  public final ImageView flag;

  @NonNull
  public final ImageView vipBadge;

  private ItemServerBinding(@NonNull CardView rootView, @NonNull ImageView btnSelected,
      @NonNull TextView countryName, @NonNull ImageView flag, @NonNull ImageView vipBadge) {
    this.rootView = rootView;
    this.btnSelected = btnSelected;
    this.countryName = countryName;
    this.flag = flag;
    this.vipBadge = vipBadge;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemServerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemServerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_server, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemServerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_selected;
      ImageView btnSelected = ViewBindings.findChildViewById(rootView, id);
      if (btnSelected == null) {
        break missingId;
      }

      id = R.id.countryName;
      TextView countryName = ViewBindings.findChildViewById(rootView, id);
      if (countryName == null) {
        break missingId;
      }

      id = R.id.flag;
      ImageView flag = ViewBindings.findChildViewById(rootView, id);
      if (flag == null) {
        break missingId;
      }

      id = R.id.vip_badge;
      ImageView vipBadge = ViewBindings.findChildViewById(rootView, id);
      if (vipBadge == null) {
        break missingId;
      }

      return new ItemServerBinding((CardView) rootView, btnSelected, countryName, flag, vipBadge);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
