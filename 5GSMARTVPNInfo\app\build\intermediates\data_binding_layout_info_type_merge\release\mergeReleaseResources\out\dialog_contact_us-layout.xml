<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_contact_us" modulePackage="com.official.fivegfastvpn" filePath="app\src\main\res\layout\dialog_contact_us.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/dialog_contact_us_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="184" endOffset="35"/></Target><Target id="@+id/contact_icon" view="ImageView"><Expressions/><location startLine="21" startOffset="12" endLine="27" endOffset="45"/></Target><Target id="@+id/email_card" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="57" startOffset="12" endLine="94" endOffset="47"/></Target><Target id="@+id/name_input_layout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="106" startOffset="12" endLine="119" endOffset="67"/></Target><Target id="@+id/name_input" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="114" startOffset="16" endLine="118" endOffset="56"/></Target><Target id="@+id/email_input_layout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="121" startOffset="12" endLine="134" endOffset="67"/></Target><Target id="@+id/email_input" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="129" startOffset="16" endLine="133" endOffset="58"/></Target><Target id="@+id/message_input_layout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="136" startOffset="12" endLine="151" endOffset="67"/></Target><Target id="@+id/message_input" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="144" startOffset="16" endLine="150" endOffset="39"/></Target><Target id="@+id/contact_cancel_button" view="Button"><Expressions/><location startLine="163" startOffset="12" endLine="171" endOffset="51"/></Target><Target id="@+id/contact_send_button" view="Button"><Expressions/><location startLine="173" startOffset="12" endLine="181" endOffset="50"/></Target></Targets></Layout>