<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#6850F7"
    tools:context=".SplashActivity">

    <!-- Child RelativeLayout with vpn background -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/vpnhome">

        <ImageView
            android:id="@+id/logo"
            android:layout_width="150dp"
            android:layout_height="150dp"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="100dp"
            android:src="@drawable/fivegsmartvpn" />

        <TextView
            android:id="@+id/name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/logo"
            android:layout_marginTop="30dp"
            android:gravity="center"
            android:text="@string/app_name"
            android:textColor="@color/white"
            android:textSize="20dp"
            android:textStyle="bold" />

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/animationView"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:layout_below="@id/name"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="20dp"
            android:padding="19dp"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_rawRes="@raw/loading" />

    </RelativeLayout>

</RelativeLayout>
