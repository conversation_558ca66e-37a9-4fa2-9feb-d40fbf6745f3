// Generated by view binder compiler. Do not edit!
package com.official.fivegfastvpn.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.RelativeLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.official.fivegfastvpn.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ProActivityPremiumBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final CardView annualPlan;

  @NonNull
  public final ImageButton btnClose;

  @NonNull
  public final MaterialButton btnSubscribe;

  @NonNull
  public final CardView monthlyPlan;

  private ProActivityPremiumBinding(@NonNull RelativeLayout rootView, @NonNull CardView annualPlan,
      @NonNull ImageButton btnClose, @NonNull MaterialButton btnSubscribe,
      @NonNull CardView monthlyPlan) {
    this.rootView = rootView;
    this.annualPlan = annualPlan;
    this.btnClose = btnClose;
    this.btnSubscribe = btnSubscribe;
    this.monthlyPlan = monthlyPlan;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ProActivityPremiumBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ProActivityPremiumBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.pro_activity_premium, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ProActivityPremiumBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.annual_plan;
      CardView annualPlan = ViewBindings.findChildViewById(rootView, id);
      if (annualPlan == null) {
        break missingId;
      }

      id = R.id.btn_close;
      ImageButton btnClose = ViewBindings.findChildViewById(rootView, id);
      if (btnClose == null) {
        break missingId;
      }

      id = R.id.btn_subscribe;
      MaterialButton btnSubscribe = ViewBindings.findChildViewById(rootView, id);
      if (btnSubscribe == null) {
        break missingId;
      }

      id = R.id.monthly_plan;
      CardView monthlyPlan = ViewBindings.findChildViewById(rootView, id);
      if (monthlyPlan == null) {
        break missingId;
      }

      return new ProActivityPremiumBinding((RelativeLayout) rootView, annualPlan, btnClose,
          btnSubscribe, monthlyPlan);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
