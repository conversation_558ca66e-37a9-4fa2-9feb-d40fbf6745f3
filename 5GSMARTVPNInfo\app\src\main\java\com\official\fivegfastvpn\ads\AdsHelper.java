package com.official.fivegfastvpn.ads;

import android.content.Context;

import com.official.fivegfastvpn.pro.ProConfig;

//Developer :--<PERSON><PERSON><PERSON>
public class AdsHelper {
    public static String admob_id = "";
    public static String admob_banner = "";
    public static String admob_interstitial = "";
    public static String admob_rewarded = "";
    public static String reward_time = "";
    public static String admob_native = "";
    public static String admob_open_ad = "";

    // Random Ad Timing Settings
    public static boolean randomAdTimingEnabled = true;
    public static int maxUsageTimeMinutes = 10;
    public static String interstitialTimingIntervals = "3,4,5,9";
    public static String facebook_id = "";
    public static String facebook_banner = "";
    public static String facebook_interstitial = "";
    public static String facebook_rewarded = "";
    public static String facebook_native = "";
    public static String banner_type = "";
    public static String interstitial_type = "";
    public static String rewarded_type = "";
    public static String native_type = "";

    public static boolean isAds = false;
    public static boolean isOpenAdEnabled = false;


    public static boolean isDisableAds(Context context) {
        if (ProConfig.isPremium(context)) {
            return true;
        } else {
            return !AdsHelper.isAds;
        }
    }

    /**
     * Check if open ads are enabled both globally and specifically
     * @param context Application context
     * @return true if open ads should be shown
     */
    public static boolean isOpenAdAllowed(Context context) {
        if (ProConfig.isPremium(context)) {
            return false; // Premium users don't see ads
        }
        return AdsHelper.isAds && AdsHelper.isOpenAdEnabled;
    }

}
