<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.1" type="conditional_incidents">

    <incident
        id="UnspecifiedImmutableFlag"
        severity="fatal"
        message="Missing `PendingIntent` mutability flag">
        <fix-alternatives>
            <fix-replace
                description="Add FLAG_IMMUTABLE (preferred)"
                replacement="android.app.PendingIntent.FLAG_IMMUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="13699"
                    endOffset="13700"/>
            </fix-replace>
            <fix-replace
                description="Add FLAG_MUTABLE"
                replacement="android.app.PendingIntent.FLAG_MUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="13699"
                    endOffset="13700"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
            line="338"
            column="80"
            startOffset="13699"
            endLine="338"
            endColumn="81"
            endOffset="13700"/>
    </incident>

    <incident
        id="UnspecifiedImmutableFlag"
        severity="fatal"
        message="Missing `PendingIntent` mutability flag">
        <fix-alternatives>
            <fix-replace
                description="Add FLAG_IMMUTABLE (preferred)"
                replacement="android.app.PendingIntent.FLAG_IMMUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="18548"
                    endOffset="18549"/>
            </fix-replace>
            <fix-replace
                description="Add FLAG_MUTABLE"
                replacement="android.app.PendingIntent.FLAG_MUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="18548"
                    endOffset="18549"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
            line="463"
            column="89"
            startOffset="18548"
            endLine="463"
            endColumn="90"
            endOffset="18549"/>
    </incident>

    <incident
        id="UnspecifiedImmutableFlag"
        severity="fatal"
        message="Missing `PendingIntent` mutability flag">
        <fix-alternatives>
            <fix-replace
                description="Add FLAG_IMMUTABLE (preferred)"
                replacement="android.app.PendingIntent.FLAG_IMMUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="19233"
                    endOffset="19234"/>
            </fix-replace>
            <fix-replace
                description="Add FLAG_MUTABLE"
                replacement="android.app.PendingIntent.FLAG_MUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="19233"
                    endOffset="19234"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
            line="476"
            column="80"
            startOffset="19233"
            endLine="476"
            endColumn="81"
            endOffset="19234"/>
    </incident>

    <incident
        id="UnspecifiedImmutableFlag"
        severity="fatal"
        message="Missing `PendingIntent` mutability flag">
        <fix-alternatives>
            <fix-replace
                description="Add FLAG_IMMUTABLE (preferred)"
                replacement="android.app.PendingIntent.FLAG_IMMUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="19764"
                    endOffset="19765"/>
            </fix-replace>
            <fix-replace
                description="Add FLAG_MUTABLE"
                replacement="android.app.PendingIntent.FLAG_MUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="19764"
                    endOffset="19765"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
            line="486"
            column="80"
            startOffset="19764"
            endLine="486"
            endColumn="81"
            endOffset="19765"/>
    </incident>

    <incident
        id="UnspecifiedImmutableFlag"
        severity="fatal"
        message="Missing `PendingIntent` mutability flag">
        <fix-alternatives>
            <fix-replace
                description="Add FLAG_IMMUTABLE (preferred)"
                replacement="android.app.PendingIntent.FLAG_IMMUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="20288"
                    endOffset="20289"/>
            </fix-replace>
            <fix-replace
                description="Add FLAG_MUTABLE"
                replacement="android.app.PendingIntent.FLAG_MUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="20288"
                    endOffset="20289"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
            line="498"
            column="77"
            startOffset="20288"
            endLine="498"
            endColumn="78"
            endOffset="20289"/>
    </incident>

    <incident
        id="UnspecifiedImmutableFlag"
        severity="fatal"
        message="Missing `PendingIntent` mutability flag">
        <fix-alternatives>
            <fix-replace
                description="Add FLAG_IMMUTABLE (preferred)"
                replacement="android.app.PendingIntent.FLAG_IMMUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="20986"
                    endOffset="20987"/>
            </fix-replace>
            <fix-replace
                description="Add FLAG_MUTABLE"
                replacement="android.app.PendingIntent.FLAG_MUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="20986"
                    endOffset="20987"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
            line="519"
            column="66"
            startOffset="20986"
            endLine="519"
            endColumn="67"
            endOffset="20987"/>
    </incident>

    <incident
        id="UnspecifiedImmutableFlag"
        severity="fatal"
        message="Missing `PendingIntent` mutability flag">
        <fix-alternatives>
            <fix-replace
                description="Add FLAG_IMMUTABLE (preferred)"
                replacement="android.app.PendingIntent.FLAG_IMMUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="56064"
                    endOffset="56065"/>
            </fix-replace>
            <fix-replace
                description="Add FLAG_MUTABLE"
                replacement="android.app.PendingIntent.FLAG_MUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="56064"
                    endOffset="56065"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
            line="1411"
            column="76"
            startOffset="56064"
            endLine="1411"
            endColumn="77"
            endOffset="56065"/>
    </incident>

    <incident
        id="UnspecifiedImmutableFlag"
        severity="fatal"
        message="Missing `PendingIntent` mutability flag">
        <fix-alternatives>
            <fix-replace
                description="Add FLAG_IMMUTABLE (preferred)"
                replacement="android.app.PendingIntent.FLAG_IMMUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNServiceV2.java"
                    startOffset="22535"
                    endOffset="22536"/>
            </fix-replace>
            <fix-replace
                description="Add FLAG_MUTABLE"
                replacement="android.app.PendingIntent.FLAG_MUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNServiceV2.java"
                    startOffset="22535"
                    endOffset="22536"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNServiceV2.java"
            line="612"
            column="88"
            startOffset="22535"
            endLine="612"
            endColumn="89"
            endOffset="22536"/>
    </incident>

    <incident
        id="UnspecifiedImmutableFlag"
        severity="fatal"
        message="Missing `PendingIntent` mutability flag">
        <fix-alternatives>
            <fix-replace
                description="Add FLAG_IMMUTABLE (preferred)"
                replacement="android.app.PendingIntent.FLAG_IMMUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNServiceV2.java"
                    startOffset="23665"
                    endOffset="23666"/>
            </fix-replace>
            <fix-replace
                description="Add FLAG_MUTABLE"
                replacement="android.app.PendingIntent.FLAG_MUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNServiceV2.java"
                    startOffset="23665"
                    endOffset="23666"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNServiceV2.java"
            line="635"
            column="99"
            startOffset="23665"
            endLine="635"
            endColumn="100"
            endOffset="23666"/>
    </incident>

</incidents>
