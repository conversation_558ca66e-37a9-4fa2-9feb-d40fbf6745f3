<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_main" modulePackage="com.official.fivegfastvpn" filePath="app\src\main\res\layout\fragment_main.xml" directory="layout" isMerge="false" isBindingData="true" rootNodeType="androidx.core.widget.NestedScrollView"><Targets><Target tag="layout/fragment_main_0" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="4" startOffset="4" endLine="345" endOffset="43"/></Target><Target tag="binding_1" view="LinearLayout"><Expressions/><location startLine="9" startOffset="8" endLine="344" endOffset="22"/></Target><Target tag="binding_1" include="native_item_ads_container"><Expressions/><location startLine="341" startOffset="12" endLine="341" endOffset="65"/></Target><Target id="@+id/toolbar" view="RelativeLayout"><Expressions/><location startLine="20" startOffset="16" endLine="95" endOffset="32"/></Target><Target id="@+id/category" view="ImageView"><Expressions/><location startLine="25" startOffset="20" endLine="32" endOffset="54"/></Target><Target id="@+id/text" view="TextView"><Expressions/><location startLine="34" startOffset="20" endLine="45" endOffset="50"/></Target><Target id="@+id/notification" view="ImageView"><Expressions/><location startLine="46" startOffset="20" endLine="51" endOffset="79"/></Target><Target id="@+id/premium" view="ImageView"><Expressions/><location startLine="53" startOffset="20" endLine="59" endOffset="57"/></Target><Target id="@+id/pre" view="LinearLayout"><Expressions/><location startLine="64" startOffset="20" endLine="76" endOffset="34"/></Target><Target id="@+id/purchase_layout" view="LinearLayout"><Expressions/><location startLine="78" startOffset="20" endLine="92" endOffset="34"/></Target><Target id="@+id/currentConnectionLayout" view="RelativeLayout"><Expressions/><location startLine="107" startOffset="20" endLine="170" endOffset="36"/></Target><Target id="@+id/selectedServerIcon" view="ImageView"><Expressions/><location startLine="115" startOffset="24" endLine="122" endOffset="62"/></Target><Target id="@+id/countryName" view="TextView"><Expressions/><location startLine="130" startOffset="28" endLine="142" endOffset="57"/></Target><Target id="@+id/ipTv" view="TextView"><Expressions/><location startLine="144" startOffset="28" endLine="156" endOffset="59"/></Target><Target id="@+id/chevronRight" view="ImageView"><Expressions/><location startLine="160" startOffset="24" endLine="168" endOffset="70"/></Target><Target id="@+id/protectionStatus" view="TextView"><Expressions/><location startLine="172" startOffset="20" endLine="186" endOffset="49"/></Target><Target id="@+id/contime1" view="LinearLayout"><Expressions/><location startLine="188" startOffset="20" endLine="220" endOffset="34"/></Target><Target id="@+id/durationTv" view="TextView"><Expressions/><location startLine="196" startOffset="24" endLine="207" endOffset="58"/></Target><Target id="@+id/ipAddress" view="TextView"><Expressions/><location startLine="209" startOffset="24" endLine="218" endOffset="53"/></Target><Target id="@+id/statsLayout" view="LinearLayout"><Expressions/><location startLine="222" startOffset="20" endLine="281" endOffset="34"/></Target><Target id="@+id/byteInTv" view="TextView"><Expressions/><location startLine="238" startOffset="28" endLine="245" endOffset="58"/></Target><Target id="@+id/byteOutTv" view="TextView"><Expressions/><location startLine="263" startOffset="28" endLine="270" endOffset="58"/></Target><Target id="@+id/btnConnect" view="com.airbnb.lottie.LottieAnimationView"><Expressions/><location startLine="283" startOffset="20" endLine="292" endOffset="58"/></Target><Target id="@+id/logTv" view="TextView"><Expressions/><location startLine="294" startOffset="20" endLine="303" endOffset="49"/></Target><Target id="@+id/renewButtonLayout" view="LinearLayout"><Expressions/><location startLine="306" startOffset="20" endLine="332" endOffset="34"/></Target><Target id="@+id/btnRenew" view="Button"><Expressions/><location startLine="316" startOffset="24" endLine="322" endOffset="53"/></Target><Target id="@+id/extraTime" view="Button"><Expressions/><location startLine="323" startOffset="24" endLine="331" endOffset="53"/></Target></Targets></Layout>