<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res"><file name="tab_text" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\color\tab_text.xml" qualifiers="" type="color"/><file name="fivegsmartvpn" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable\fivegsmartvpn.jpg" qualifiers="" type="drawable"/><file name="ic_notification" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable\ic_notification.xml" qualifiers="" type="drawable"/><file name="ic_menu_archive" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable-hdpi\ic_menu_archive.png" qualifiers="hdpi-v4" type="drawable"/><file name="ic_menu_copy_holo_light" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable-hdpi\ic_menu_copy_holo_light.png" qualifiers="hdpi-v4" type="drawable"/><file name="ic_menu_log" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable-hdpi\ic_menu_log.png" qualifiers="hdpi-v4" type="drawable"/><file name="ic_quick" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable-hdpi\ic_quick.png" qualifiers="hdpi-v4" type="drawable"/><file name="ic_stat_vpn" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable-hdpi\ic_stat_vpn.png" qualifiers="hdpi-v4" type="drawable"/><file name="ic_stat_vpn_empty_halo" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable-hdpi\ic_stat_vpn_empty_halo.png" qualifiers="hdpi-v4" type="drawable"/><file name="ic_stat_vpn_offline" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable-hdpi\ic_stat_vpn_offline.png" qualifiers="hdpi-v4" type="drawable"/><file name="ic_stat_vpn_outline" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable-hdpi\ic_stat_vpn_outline.png" qualifiers="hdpi-v4" type="drawable"/><file name="vpn_item_settings" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable-hdpi\vpn_item_settings.png" qualifiers="hdpi-v4" type="drawable"/><file name="ic_menu_archive" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable-mdpi\ic_menu_archive.png" qualifiers="mdpi-v4" type="drawable"/><file name="ic_menu_copy_holo_light" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable-mdpi\ic_menu_copy_holo_light.png" qualifiers="mdpi-v4" type="drawable"/><file name="ic_menu_log" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable-mdpi\ic_menu_log.png" qualifiers="mdpi-v4" type="drawable"/><file name="ic_quick" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable-mdpi\ic_quick.png" qualifiers="mdpi-v4" type="drawable"/><file name="ic_stat_vpn" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable-mdpi\ic_stat_vpn.png" qualifiers="mdpi-v4" type="drawable"/><file name="ic_stat_vpn_empty_halo" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable-mdpi\ic_stat_vpn_empty_halo.png" qualifiers="mdpi-v4" type="drawable"/><file name="ic_stat_vpn_offline" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable-mdpi\ic_stat_vpn_offline.png" qualifiers="mdpi-v4" type="drawable"/><file name="ic_stat_vpn_outline" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable-mdpi\ic_stat_vpn_outline.png" qualifiers="mdpi-v4" type="drawable"/><file name="vpn_item_settings" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable-mdpi\vpn_item_settings.png" qualifiers="mdpi-v4" type="drawable"/><file name="ic_menu_archive" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable-xhdpi\ic_menu_archive.png" qualifiers="xhdpi-v4" type="drawable"/><file name="ic_menu_copy_holo_light" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable-xhdpi\ic_menu_copy_holo_light.png" qualifiers="xhdpi-v4" type="drawable"/><file name="ic_menu_log" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable-xhdpi\ic_menu_log.png" qualifiers="xhdpi-v4" type="drawable"/><file name="ic_quick" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable-xhdpi\ic_quick.png" qualifiers="xhdpi-v4" type="drawable"/><file name="ic_stat_vpn" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable-xhdpi\ic_stat_vpn.png" qualifiers="xhdpi-v4" type="drawable"/><file name="ic_stat_vpn_empty_halo" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable-xhdpi\ic_stat_vpn_empty_halo.png" qualifiers="xhdpi-v4" type="drawable"/><file name="ic_stat_vpn_offline" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable-xhdpi\ic_stat_vpn_offline.png" qualifiers="xhdpi-v4" type="drawable"/><file name="ic_stat_vpn_outline" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable-xhdpi\ic_stat_vpn_outline.png" qualifiers="xhdpi-v4" type="drawable"/><file name="vpn_item_settings" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable-xhdpi\vpn_item_settings.png" qualifiers="xhdpi-v4" type="drawable"/><file name="ic_menu_copy_holo_light" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable-xxhdpi\ic_menu_copy_holo_light.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="ic_menu_log" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable-xxhdpi\ic_menu_log.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="ic_quick" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable-xxhdpi\ic_quick.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="ic_stat_vpn" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable-xxhdpi\ic_stat_vpn.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="ic_stat_vpn_empty_halo" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable-xxhdpi\ic_stat_vpn_empty_halo.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="ic_stat_vpn_offline" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable-xxhdpi\ic_stat_vpn_offline.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="ic_stat_vpn_outline" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\drawable-xxhdpi\ic_stat_vpn_outline.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="api_confirm" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\layout\api_confirm.xml" qualifiers="" type="layout"/><file name="import_as_config" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\layout\import_as_config.xml" qualifiers="" type="layout"/><file name="launchvpn" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\layout\launchvpn.xml" qualifiers="" type="layout"/><file name="userpass" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\layout\userpass.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="banner_tv" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\mipmap-xhdpi\banner_tv.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\values\arrays.xml" qualifiers=""><string-array name="vpn_types">
        <item>Certificates</item>
        <item>PKCS12 File</item>
        <item>Android Certificate</item>
        <item>Username/Password</item>
        <item>Static Keys</item>
        <item>User/PW + Certificates</item>
        <item>User/PW + PKCS12 </item>
        <item>User/PW + Android</item>
        <item>External Auth Provider</item>
    </string-array><string-array name="tls_directions_entries">
        <item translatable="false">0</item>
        <item translatable="false">1</item>
        <item>Unspecified</item>
        <item>Encryption (--tls-crypt)</item>
        <item>TLS Crypt V2</item>
    </string-array><string-array name="crm_entries">
        <item>No reconnection retries</item>
        <item>One reconnection retry</item>
        <item>Five reconnection retries</item>
        <item>Fifty reconnection retries</item>
        <item>Unlimited reconnection retries</item>
    </string-array><string-array name="auth_retry_type">
        <item>Disconnect, forget password</item>
        <item>Disconnect, keep password</item>
        <item>Ignore, retry</item>
    </string-array></file><file path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\values\attrs.xml" qualifiers=""><declare-styleable name="FileSelectLayout">
       <attr format="string|reference" name="fileTitle"/>
      <attr format="boolean" name="certificate"/>

       <attr format="boolean" name="showClear"/>
   </declare-styleable><declare-styleable name="PagerSlidingTabStrip">
        <attr format="color" name="pstsIndicatorColor"/>
        <attr format="color" name="pstsUnderlineColor"/>
        <attr format="color" name="pstsDividerColor"/>
        <attr format="dimension" name="pstsDividerWidth"/>
        <attr format="dimension" name="pstsIndicatorHeight"/>
        <attr format="dimension" name="pstsUnderlineHeight"/>
        <attr format="dimension" name="pstsDividerPadding"/>
        <attr format="dimension" name="pstsTabPaddingLeftRight"/>
        <attr format="dimension" name="pstsScrollOffset"/>
        <attr format="reference" name="pstsTabBackground"/>
        <attr format="boolean" name="pstsShouldExpand"/>
        <attr format="boolean" name="pstsTextAllCaps"/>
        <attr format="boolean" name="pstsPaddingMiddle"/>
        <attr name="pstsTextStyle">
            <flag name="normal" value="0x0"/>
            <flag name="bold" value="0x1"/>
            <flag name="italic" value="0x2"/>
        </attr>
        <attr name="pstsTextSelectedStyle">
            <flag name="normal" value="0x0"/>
            <flag name="bold" value="0x1"/>
            <flag name="italic" value="0x2"/>
        </attr>
        <attr format="float" name="pstsTextAlpha"/>
        <attr format="float" name="pstsTextSelectedAlpha"/>
    </declare-styleable></file><file path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\values\bools.xml" qualifiers=""><bool name="supportFileScheme">true</bool></file><file path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\values\colours.xml" qualifiers=""><color name="primary">#3F51B5</color><color name="primary_dark">#303F9F</color><color name="accent">#FFA726</color><color name="gelb">#ffff00</color><color name="rot">#ff0000</color><color name="switchbar">@android:color/darker_gray</color><color name="background_tab_pressed">#1AFFFFFF</color><color name="dataIn">#ff0000</color><color name="dataOut">#0000ff</color></file><file path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\values\dimens.xml" qualifiers=""><dimen name="paddingItemsSidebarLog">20dp</dimen><dimen name="stdpadding">8dp</dimen><bool name="logSildersAlwaysVisible">false</bool><dimen name="diameter">48dp</dimen><dimen name="elevation_low">1dp</dimen><dimen name="elevation_high">4dp</dimen><dimen name="add_button_margin">16dp</dimen><dimen name="add_button_margin_topfab">96dp</dimen><dimen name="round_button_diameter">56dp</dimen><dimen name="switchbar_pad">16dp</dimen><dimen name="vpn_setting_padding">16dp</dimen></file><file path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\values\plurals.xml" qualifiers=""><plurals name="months_left">
        <item quantity="one">One month left</item>
        <item quantity="other">%d months left</item>
    </plurals><plurals name="days_left">
        <item quantity="one">One day left</item>
        <item quantity="other">%d days left</item>
    </plurals><plurals name="hours_left">
        <item quantity="one">One hour left</item>
        <item quantity="other">%d hours left</item>
    </plurals><plurals name="minutes_left">
        <item quantity="one">One minute left</item>
        <item quantity="other">%d minutes left</item>
    </plurals></file><file path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\values\refs.xml" qualifiers=""><drawable name="ic_menu_close_clear_cancel">@android:drawable/ic_menu_close_clear_cancel</drawable><drawable name="ic_menu_play">@android:drawable/ic_media_play</drawable><drawable name="ic_menu_pause">@android:drawable/ic_media_pause</drawable></file><file path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\values\strings.xml" qualifiers=""><string name="app">OpenVPN for Android</string><string name="address">Server Address:</string><string name="port">Server Port:</string><string name="location">Location</string><string name="cant_read_folder">Unable to read directory</string><string name="select">Select</string><string name="cancel">Cancel</string><string name="no_data">No Data</string><string name="useLZO">LZO Compression</string><string name="client_no_certificate">No Certificate</string><string name="client_certificate_title">Client Certificate</string><string name="client_key_title">Client Certificate Key</string><string name="client_pkcs12_title">PKCS12 File</string><string name="ca_title">CA Certificate</string><string name="no_certificate">You must select a certificate</string><string name="copyright_guicode">Source code and issue tracker available at https://github.com/schwabe/ics-openvpn/</string><string name="copyright_others">This program uses the following components; see the source code for full details on the licenses</string><string name="about">About</string><string name="vpn_list_title">Profiles</string><string name="vpn_type">Type</string><string name="pkcs12pwquery">PKCS12 Password</string><string name="file_select">Select…</string><string name="file_nothing_selected">You must select a file</string><string name="useTLSAuth">Use TLS Authentication</string><string name="tls_direction">TLS Direction</string><string name="ipv6_dialog_tile">Enter IPv6 Address/Netmask in CIDR Format (e.g. 2000:dd::23/64)</string><string name="ipv4_dialog_title">Enter IPv4 Address/Netmask in CIDR Format (e.g. *******/24)</string><string name="ipv4_address">IPv4 Address</string><string name="ipv6_address">IPv6 Address</string><string name="custom_option_warning">Enter custom OpenVPN options. Use with caution. Also note that many of the tun related OpenVPN settings cannot be supported by design of the VPNSettings. If you think an important option is missing contact the author</string><string name="auth_username">Username</string><string name="auth_pwquery">Password</string><string name="static_keys_info">For the static configuration the TLS Auth Keys will be used as static keys</string><string name="configure_the_vpn">Configure the VPN</string><string name="menu_add_profile">Add Profile</string><string name="add_profile_name_prompt">Enter a name identifying the new Profile</string><string name="duplicate_profile_name">Please enter a unique Profile Name</string><string name="profilename">Profile Name</string><string name="no_keystore_cert_selected">You must select a User certificate</string><string name="no_ca_cert_selected">You must select a CA certificate</string><string name="no_error_found">No error found</string><string name="config_error_found">Error in Configuration</string><string name="ipv4_format_error">Error parsing the IPv4 address</string><string name="custom_route_format_error">Error parsing the custom routes</string><string name="pw_query_hint">(leave empty to query on demand)</string><string name="vpn_shortcut">OpenVPN Shortcut</string><string name="vpn_launch_title">Connecting to VPN…</string><string name="shortcut_profile_notfound">Profile specified in shortcut not found</string><string name="random_host_prefix">Random Host Prefix</string><string name="random_host_summary">Adds 6 random chars in front of hostname</string><string name="custom_config_title">Enable Custom Options</string><string name="custom_config_summary">Specify custom options. Use with care!</string><string name="route_rejected">Route rejected by Android</string><string name="cancel_connection">Disconnect</string><string name="cancel_connection_long">Disconnect VPN</string><string name="clear_log">clear log</string><string name="title_cancel">Cancel Confirmation</string><string name="cancel_connection_query">Disconnect the connected VPN/cancel the connection attempt?</string><string name="remove_vpn">Remove VPN</string><string name="check_remote_tlscert">Checks whether the server uses a certificate with TLS Server extensions (--remote-cert-tls server)</string><string name="check_remote_tlscert_title">Expect TLS server certificate</string><string name="remote_tlscn_check_summary">Checks the Remote Server Certificate Subject DN</string><string name="remote_tlscn_check_title">Certificate Hostname Check</string><string name="enter_tlscn_dialog">Specify the check used to verify the remote certificate DN (e.g. C=DE, L=Paderborn, OU=Avian IP Carriers, CN=openvpn.blinkt.de)\n\nSpecify the complete DN or the RDN (openvpn.blinkt.de in the example) or an RDN prefix for verification.\n\nWhen using RDN prefix \"Server\" matches \"Server-1\" and \"Server-2\"\n\nLeaving the text field empty will check the RDN against the server hostname.\n\nFor more details see the OpenVPN 2.3.1+ manpage under —verify-x509-name</string><string name="enter_tlscn_title">Remote certificate subject</string><string name="tls_key_auth">Enables the TLS Key Authentication</string><string name="tls_auth_file">TLS Auth File</string><string name="pull_on_summary">Requests IP addresses, routes and timing options from the server.</string><string name="pull_off_summary">No information is requested from the server. Settings need to be specified below.</string><string name="use_pull">Pull Settings</string><string name="dns">DNS</string><string name="override_dns">Override DNS Settings by Server</string><string name="dns_override_summary">Use your own DNS Servers</string><string name="searchdomain">searchDomain</string><string name="dns1_summary">DNS Server to be used.</string><string name="dns_server">DNS Server</string><string name="secondary_dns_message">Secondary DNS Server used if the normal DNS Server cannot be reached.</string><string name="backup_dns">Backup DNS Server</string><string name="ignored_pushed_routes">Ignore pushed routes</string><string name="ignore_routes_summary">Ignore routes pushed by the server.</string><string name="default_route_summary">Redirects all Traffic over the VPN</string><string name="use_default_title">Use default Route</string><string name="custom_route_message">Enter custom routes. Only enter destination in CIDR format. \"10.0.0.0/8 2002::/16\" would direct the networks 10.0.0.0/8 and 2002::/16 over the VPN.</string><string name="custom_route_message_excluded">Routes that should NOT be routed over the VPN. Use the same syntax as for included routes.</string><string name="custom_routes_title">Custom Routes</string><string name="custom_routes_title_excluded">Excluded Networks</string><string name="log_verbosity_level">Log verbosity level</string><string name="float_summary">Allows authenticated packets from any IP</string><string name="float_title">Allow floating server</string><string name="custom_options_title">Custom Options</string><string name="edit_vpn">Edit VPN Settings</string><string name="remove_vpn_query">Remove the VPN Profile \'%s\'?</string><string name="tun_error_helpful">On some custom ICS images the permission on /dev/tun might be wrong, or the tun module might be missing completely. For CM9 images try the fix ownership option under general settings</string><string name="tun_open_error">Failed to open the tun interface</string><string name="error">"Error: "</string><string name="clear">Clear</string><string name="last_openvpn_tun_config">Opening tun interface:</string><string name="local_ip_info">Local IPv4: %1$s/%2$d IPv6: %3$s MTU: %4$d</string><string name="dns_server_info">DNS Server: %1$s, Domain: %2$s</string><string name="routes_info_incl">Routes: %1$s %2$s</string><string name="routes_info_excl">Routes excluded: %1$s %2$s</string><string name="routes_debug">VpnService routes installed: %1$s %2$s</string><string name="ip_not_cidr">Got interface information %1$s and %2$s, assuming second address is peer address of remote. Using /32 netmask for local IP. Mode given by OpenVPN is \"%3$s\".</string><string name="route_not_cidr">Cannot make sense of %1$s and %2$s as IP route with CIDR netmask, using /32 as netmask.</string><string name="route_not_netip">Corrected route %1$s/%2$s to %3$s/%2$s</string><string name="keychain_access">Cannot access the Android Keychain Certificates. This can be caused by a firmware upgrade or by restoring a backup of the app/app settings. Please edit the VPN and reselect the certificate under basic settings to recreate the permission to access the certificate.</string><string name="version_info">%1$s %2$s</string><string name="send_logfile">Send log file</string><string name="send">Send</string><string name="ics_openvpn_log_file">ICS OpenVPN log file</string><string name="copied_entry">Copied log entry to clip board</string><string name="tap_mode">Tap Mode</string><string name="faq_tap_mode">Tap Mode is not possible with the non root VPN API. Therefore this application cannot provide tap support</string><string name="tap_faq2">Again? Are you kidding? No, tap mode is really not supported and sending more mail asking if it will be supported will not help.</string><string name="tap_faq3">A third time? Actually, one could write a tap emulator based on tun that would add layer2 information on send and strip layer2 information on receive. But this tap emulator would also have to implement ARP and possibly a DHCP client. I am not aware of anybody doing any work in this direction. Contact me if you want to start coding on this.</string><string name="faq">FAQ</string><string name="copying_log_entries">Copying log entries</string><string name="faq_copying">To copy a single log entry press and hold on the log entry. To copy/send the whole log use the Send Log option. Use the hardware menu button, if the button is not visible in the GUI.</string><string name="faq_shortcut">Shortcut to start</string><string name="faq_howto_shortcut">You can place a shortcut to start OpenVPN on your desktop. Depending on your homescreen program you will have to add either a shortcut or a widget.</string><string name="no_vpn_support_image">Your image does not support the VPNService API, sorry :(</string><string name="encryption">Encryption</string><string name="cipher_dialog_title">Enter encryption method</string><string name="chipher_dialog_message">Enter the encryption cipher algorithm used by OpenVPN. Leave empty to use default cipher.</string><string name="auth_dialog_message">Enter the authentication digest used for OpenVPN. Leave empty to use default digest.</string><string name="settings_auth">Authentication/Encryption</string><string name="file_explorer_tab">File Explorer</string><string name="inline_file_tab">Inline File</string><string name="error_importing_file">Error importing File</string><string name="import_error_message">Could not import File from filesystem</string><string name="inline_file_data">[[Inline file data]]</string><string name="opentun_no_ipaddr">Refusing to open tun device without IP information</string><string name="menu_import">Import Profile from ovpn file</string><string name="menu_import_short">Import</string><string name="import_content_resolve_error">Could not read profile to import</string><string name="error_reading_config_file">Error reading config file</string><string name="add_profile">add Profile</string><string name="import_could_not_open">Could not find file %1$s mentioned in the imported config file</string><string name="importing_config">Importing config file from source %1$s</string><string name="import_warning_custom_options">Your configuration had a few configuration options that are not mapped to UI configurations. These options were added as custom configuration options. The custom configuration is displayed below:</string><string name="import_done">Done reading config file.</string><string name="nobind_summary">Do not bind to local address and port</string><string name="no_bind">No local binding</string><string name="import_configuration_file">Import configuration file</string><string name="faq_security_title">Security considerations</string><string name="faq_security">"As OpenVPN is security sensitive a few notes about security are sensible. All data on the sdcard is inherently insecure. Every app can read it (for example this program requires no special sd card rights). The data of this application can only be read by the application itself. By using the import option for cacert/cert/key in the file dialog the data is stored in the VPN profile. The VPN profiles are only accessible by this application. (Do not forget to delete the copies on the sd card afterwards). Even though accessible only by this application the data is still unencrypted. By rooting the telephone or other exploits it may be possible to retrieve the data. Saved passwords are stored in plain text as well. For pkcs12 files it is highly recommended that you import them into the android keystore."</string><string name="import_vpn">Import</string><string name="broken_image_cert_title">Error showing certificate selection</string><string name="broken_image_cert">Got an exception trying to show the Android 4.0+ certificate selection dialog. This should never happen as this a standard feature of Android 4.0+. Maybe your Android ROM support for certificate storage is broken</string><string name="ipv4">IPv4</string><string name="ipv6">IPv6</string><string name="speed_waiting">Waiting for state message…</string><string name="converted_profile">imported profile</string><string name="converted_profile_i">imported profile %d</string><string name="broken_images">Broken Images</string><string name="broken_images_faq">&lt;p>Official HTC images are known to have a strange routing problem causing traffic not to flow through the tunnel (See also &lt;a href="https://github.com/schwabe/ics-openvpn/issues/18">Issue 18&lt;/a> in the bug tracker.)&lt;/p>&lt;p>Older official SONY images from Xperia Arc S and Xperia Ray have been reported to be missing the VPNService completely from the image. (See also &lt;a href="https://github.com/schwabe/ics-openvpn/issues/29">Issue 29&lt;/a> in the bug tracker.)&lt;/p>&lt;p>On custom build images the tun module might be missing or the rights of /dev/tun might be wrong. Some CM9 images need the "Fix ownership" option under "Device specific hacks" enabled.&lt;/p>&lt;p>Most importantly: If your device has a broken Android image, report it to your vendor. The more people who report an issue to the vendor, the more likely they are to fix it.&lt;/p></string><string name="pkcs12_file_encryption_key">PKCS12 File Encryption Key</string><string name="private_key_password">Private Key Password</string><string name="password">Password</string><string name="file_icon">file icon</string><string name="tls_authentication">TLS Authentication/Encryption</string><string name="generated_config">Generated Config</string><string name="generalsettings">Settings</string><string name="owner_fix_summary">Tries to set the owner of /dev/tun to system. Some CM9 images need this to make the VPNService API work. Requires root.</string><string name="owner_fix">Fix ownership of /dev/tun</string><string name="generated_config_summary">Shows the generated OpenVPN Configuration File</string><string name="edit_profile_title">Editing \"%s\"</string><string name="building_configration">Building configuration…</string><string name="netchange_summary">Turning this option on will force a reconnect if the network state is changed (e.g. WiFi to/from mobile)</string><string name="netchange">Reconnect on network change</string><string name="netstatus">Network Status: %s</string><string name="extracahint">The CA cert is usually returned from the Android keystore. Specify a separate certificate if you get certificate verification errors.</string><string name="select_file">Select</string><string name="keychain_nocacert">No CA Certificate returned while reading from Android keystore. Authentication will probably fail.</string><string name="show_log_summary">Shows the log window on connect. The log window can always be accessed from the notification status.</string><string name="show_log_window">Show log window</string><string name="mobile_info">%10$s %9$s running on %3$s %1$s (%2$s), Android %6$s (%7$s) API %4$d, ABI %5$s, (%8$s)</string><string name="error_rsa_sign">Error signing with Android keystore key %1$s: %2$s</string><string name="error_extapp_sign">Error signing with external authenticator app (%3$s): %1$s: %2$s</string><string name="faq_system_dialogs">The VPN connection warning telling you that this app can intercept all traffic is imposed by the system to prevent abuse of the VPNService API.\nThe VPN connection notification (The key symbol) is also imposed by the Android system to signal an ongoing VPN connection. On some images this notification plays a sound.\nAndroid introduced these system dialogs for your own safety and made sure that they cannot be circumvented. (On some images this unfortunately includes a notification sound)</string><string name="faq_system_dialogs_title">Connection warning and notification sound</string><string name="translationby">English translation by Arne Schwabe&lt;<EMAIL>></string><string name="ipdns">IP and DNS</string><string name="basic">Basic</string><string name="routing">Routing</string><string name="obscure">Obscure OpenVPN Settings. Normally not needed.</string><string name="advanced">Advanced</string><string name="export_config_title">ICS Openvpn Config</string><string name="warn_no_dns">No DNS servers being used. Name resolution may not work. Consider setting custom DNS Servers. Please also note that Android will keep using your proxy settings specified for your mobile/Wi-Fi connection when no DNS servers are set.</string><string name="dns_add_error">Could not add DNS Server \"%1$s\", rejected by the system: %2$s</string><string name="ip_add_error">Could not configure IP Address \"%1$s\", rejected by the system: %2$s</string><string name="faq_howto">&lt;p>Get a working config (tested on your computer or download from your provider/organisation)&lt;/p>&lt;p>If it is a single file with no extra pem/pkcs12 files you can email the file yourself and open the attachment. If you have multiple files put them on your sd card.&lt;/p>&lt;p>Click on the email attachment/Use the folder icon in the vpn list to import the config file&lt;/p>&lt;p>If there are errors about missing files put the missing files on your sd card.&lt;/p>&lt;p>Click on the save symbol to add the imported VPN to your VPN list&lt;/p>&lt;p>Connect the VPN by clicking on the name of the VPN&lt;/p>&lt;p>If there are error or warnings in the log try to understand the warnings/error and try to fix them&lt;/p> </string><string name="faq_howto_title">Quick Start</string><string name="setting_loadtun_summary">Try to load the tun.ko kernel module before trying to connect. Needs rooted devices.</string><string name="setting_loadtun">Load tun module</string><string name="importpkcs12fromconfig">Import PKCS12 from configuration into Android Keystore</string><string name="getproxy_error">Error getting proxy settings: %s</string><string name="using_proxy">Using proxy %1$s %2$s</string><string name="use_system_proxy">Use system proxy</string><string name="use_system_proxy_summary">Use the system wide configuration for HTTP/HTTPS proxies to connect.</string><string name="onbootrestartsummary">OpenVPN will connect the specified VPN if it was active on system boot. Please read the connection warning FAQ before using this option on Android &lt; 5.0.</string><string name="onbootrestart">Connect on boot</string><string name="ignore">Ignore</string><string name="restart">Restart</string><string name="restart_vpn_after_change">Configuration changes are applied after restarting the VPN. (Re)start the VPN now?</string><string name="configuration_changed">Configuration changed</string><string name="log_no_last_vpn">Could not determine last connected profile for editing</string><string name="faq_duplicate_notification_title">Duplicate notifications</string><string name="faq_duplicate_notification">If Android is under system memory (RAM) pressure, apps and service which are not needed at the moment are removed from active memory. This terminates an ongoing VPN connection. To ensure that the connection/OpenVPN survives the service runs with higher priority. To run with higher priority the application must display a notification. The key notification icon is imposed by the system as described in the previous FAQ entry. It does not count as app notification for purpose of running with higher priority.</string><string name="no_vpn_profiles_defined">No VPN profiles defined.</string><string name="add_new_vpn_hint">Use the &lt;img src=\"ic_menu_add\"/> icon to add a new VPN</string><string name="vpn_import_hint">Use the &lt;img src=\"ic_menu_archive\"/> icon to import an existing (.ovpn or .conf) profile from your sdcard.</string><string name="faq_hint">Be sure to also check out the FAQ. There is a quick start guide.</string><string name="faq_routing_title">Routing/Interface Configuration</string><string name="faq_routing">The Routing and interface configuration is not done via traditional ifconfig/route commands but by using the VPNService API. This results in a different routing configuration than on other OSes. \nThe configuration of the VPN tunnel consists of the IP address and the networks that should be routed over this interface. Especially, no peer partner address or gateway address is needed or required. Special routes to reach the VPN Server (for example added when using redirect-gateway) are not needed either. The application will consequently ignore these settings when importing a configuration. The app ensures with the VPNService API that the connection to the server is not routed through the VPN tunnel.\nThe VPNService API does not allow specifying networks that should not be routed via the VPN. As a workaround the app tries to detect networks that should not be routed over tunnel (e.g. route x.x.x.x y.y.y.y net_gateway) and calculates a set of routes that excludes this routes to emulate the behaviour of other platforms. The log windows shows the configuration of the VPNService upon establishing a connection.\nBehind the scenes: Android 4.4+ does use policy routing. Using route/ifconfig will not show the installed routes. Instead use ip rule, iptables -t mangle -L</string><string name="persisttun_summary">Do not fallback to no VPN connection when OpenVPN is reconnecting.</string><string name="persistent_tun_title">Persistent tun</string><string name="openvpn_log">OpenVPN Log</string><string name="import_config">Import OpenVPN configuration</string><string name="battery_consumption_title">Battery consumption</string><string name="baterry_consumption">In my personal tests the main reason for high battery consumption of OpenVPN are the keepalive packets. Most OpenVPN servers have a configuration directive like \'keepalive 10 60\' which causes the client and server to exchange keepalive packets every ten seconds. &lt;p> While these packets are small and do not use much traffic, they keep the mobile radio network busy and increase the energy consumption. (See also &lt;a href="https://developer.android.com/training/efficient-downloads/efficient-network-access.html#RadioStateMachine">The Radio State Machine | Android Developers&lt;/a>) &lt;p> This keepalive setting cannot be changed on the client. Only the system administrator of the OpenVPN can change the setting. &lt;p> Unfortunately using a keepalive larger than 60 seconds with UDP can cause some NAT gateways to drop the connection due to an inactivity timeout. Using TCP with a long keep alive timeout works, but tunneling TCP over TCP performs extremely poorly on connections with high packet loss. (See &lt;a href="http://sites.inka.de/bigred/devel/tcp-tcp.html">Why TCP Over TCP Is A Bad Idea&lt;/a>)</string><string name="faq_tethering">The Android Tethering feature (over WiFi, USB or Bluetooth) and the VPNService API (used by this program) do not work together. For more details see the &lt;a href=\"https://github.com/schwabe/ics-openvpn/issues/34\">issue #34&lt;/a></string><string name="vpn_tethering_title">VPN and Tethering</string><string name="connection_retries">Connection retries</string><string name="reconnection_settings">Reconnection settings</string><string name="connectretrymessage">Number of seconds to wait between connection attempts.</string><string name="connectretrywait">Seconds between connections</string><string name="minidump_generated">OpenVPN crashed unexpectedly. Please consider using the send Minidump option in the main menu</string><string name="send_minidump">Send Minidump to developer</string><string name="send_minidump_summary">Sends debugging information about last crash to developer</string><string name="notifcation_title">5G SMART VPN - %s</string><string name="session_ipv4string">%1$s - %2$s</string><string name="session_ipv6string">%1$s - %3$s, %2$s</string><string name="state_connecting">Connecting</string><string name="state_wait">Waiting for server reply</string><string name="state_auth">Authenticating</string><string name="state_get_config">Getting client configuration</string><string name="state_assign_ip">Assigning IP addresses</string><string name="state_add_routes">Adding routes</string><string name="state_connected">Connected</string><string name="state_disconnected">Disconnect</string><string name="state_reconnecting">Reconnecting</string><string name="state_exiting">Exiting</string><string name="state_noprocess">Not running</string><string name="state_resolve">Resolving host names</string><string name="state_tcp_connect">Connecting (TCP)</string><string name="state_auth_failed">Authentication failed</string><string name="state_nonetwork">Waiting for usable network</string><string name="state_waitorbot">Waiting for Orbot to start</string><string name="statusline_bytecount">↓%2$s %1$s - ↑%4$s %3$s</string><string name="notifcation_title_notconnect">Not connected</string><string name="start_vpn_title">Connecting to VPN %s</string><string name="start_vpn_ticker">Connecting to VPN %s</string><string name="jelly_keystore_alphanumeric_bug">Some versions of Android 4.1 have problems if the name of the keystore certificate contains non alphanumeric characters (like spaces, underscores or dashes). Try to reimport the certificate without special characters</string><string name="encryption_cipher">Encryption cipher</string><string name="packet_auth">Packet authentication</string><string name="auth_dialog_title">Enter packet authentication method</string><string name="built_by">built by %s</string><string name="debug_build">debug build</string><string name="official_build">official build</string><string name="make_selection_inline">Copy into profile</string><string name="crashdump">Crashdump</string><string name="add">Add</string><string name="send_config">Send config file</string><string name="complete_dn">Complete DN</string><string name="remotetlsnote">Your imported configuration used the old DEPRECATED tls-remote option which uses a different DN format.</string><string name="rdn">RDN (common name)</string><string name="rdn_prefix">RDN prefix</string><string name="tls_remote_deprecated">tls-remote (DEPRECATED)</string><string name="help_translate">You can help translating by visiting https://crowdin.net/project/ics-openvpn/invite</string><string name="prompt">%1$s attempts to control %2$s</string><string name="remote_warning">By proceeding, you are giving the application permission to completely control OpenVPN for Android and to intercept all network traffic.<b>Do NOT accept unless you trust the application.</b> Otherwise, you run the risk of having your data compromised by malicious software."</string><string name="remote_trust">I trust this application.</string><string name="no_external_app_allowed">No app allowed to use external API</string><string name="allowed_apps">Allowed apps: %s</string><string name="clearappsdialog">Clear list of allowed external apps?\nCurrent list of allowed apps:\n\n%s</string><string name="screenoff_summary">Pause VPN when screen is off and less than 64 kB transferred data in 60s. When the \"Persistent Tun\" option is enabled pausing the VPN will leave your device with NO network connectivity. Without the \"Persistent Tun\" option the device will have no VPN connection/protection.</string><string name="screenoff_title">Pause VPN connection after screen off</string><string name="screenoff_pause">Pausing connection in screen off state: less than %1$s in %2$ss</string><string name="screen_nopersistenttun">Warning: Persistent tun not enabled for this VPN. Traffic will use the normal Internet connection when the screen is off.</string><string name="save_password">Save Password</string><string name="pauseVPN">Pause VPN</string><string name="resumevpn">Resume VPN</string><string name="state_userpause">VPN pause requested by user</string><string name="state_screenoff">VPN paused - screen off</string><string name="device_specific">Device specifics Hacks</string><string name="cannotparsecert">Cannot display certificate information</string><string name="appbehaviour">Application behaviour</string><string name="vpnbehaviour">VPN behaviour</string><string name="allow_vpn_changes">Allow changes to VPN Profiles</string><string name="hwkeychain">Hardware Keystore:</string><string name="permission_icon_app">Icon of app trying to use OpenVPN for Android</string><string name="faq_vpndialog43">"Starting with Android 4.3 the VPN confirmation is guarded against \"overlaying apps\". This results in the dialog not reacting to touch input. If you have an app that uses overlays it may cause this behaviour. If you find an offending app contact the author of the app. This problem affect all VPN applications on Android 4.3 and later. See also &lt;a href=\"https://github.com/schwabe/ics-openvpn/issues/185\">Issue 185&lt;a> for additional details"</string><string name="faq_vpndialog43_title">Vpn Confirmation Dialog</string><string name="donatePlayStore">Alternatively you can send me a donation with the Play Store:</string><string name="thanks_for_donation">Thanks for donating %s!</string><string name="logCleared">Log cleared.</string><string name="show_password">Show password</string><string name="keyChainAccessError">KeyChain Access error: %s</string><string name="timestamp_short">Short</string><string name="timestamp_iso">ISO</string><string name="timestamps">Timestamps</string><string name="timestamps_none">None</string><string name="uploaded_data">Upload</string><string name="downloaded_data">Download</string><string name="vpn_status">Vpn Status</string><string name="logview_options">View options</string><string name="unhandled_exception">Unhandled exception: %1$s\n\n%2$s</string><string name="unhandled_exception_context">%3$s: %1$s\n\n%2$s</string><string name="faq_system_dialog_xposed">If you have rooted your Android device you can install the &lt;a href=\"http://xposed.info/\">Xposed framework&lt;/a> and the &lt;a href=\"http://repo.xposed.info/module/de.blinkt.vpndialogxposed\">VPN Dialog confirm module&lt;/a> at your own risk"</string><string name="full_licenses">Full licenses</string><string name="blocklocal_summary">Networks directly connected to the local interfaces will not be routed over the VPN. Deselecting this option will redirect all traffic indented for local networks to the VPN.</string><string name="blocklocal_title">Bypass VPN for local networks</string><string name="userpw_file">Username/Password file</string><string name="imported_from_file">[Imported from: %s]</string><string name="files_missing_hint">Some files could not be found. Please select the files to import the profile:</string><string name="openvpn_is_no_free_vpn">To use this app you need a VPN provider/VPN gateway supporting OpenVPN (often provided by your employer). Check out https://community.openvpn.net/ for more information on OpenVPN and how to setup your own OpenVPN server.</string><string name="import_log">Import log:</string><string name="ip_looks_like_subnet">Vpn topology \"%3$s\" specified but ifconfig %1$s %2$s looks more like an IP address with a network mask. Assuming \"subnet\" topology.</string><string name="mssfix_invalid_value">The MSS override value has to be a integer between 0 and 9000</string><string name="mtu_invalid_value">The MTU override value has to be a integer between 64 and 9000</string><string name="mssfix_value_dialog">Announce to TCP sessions running over the tunnel that they should limit their send packet sizes such that after OpenVPN has encapsulated them, the resulting UDP packet size that OpenVPN sends to its peer will not exceed this number of bytes. (default is 1450)</string><string name="mssfix_checkbox">Override MSS value of TCP payload</string><string name="mssfix_dialogtitle">Set MSS of TCP payload</string><string name="client_behaviour">Client behaviour</string><string name="clear_external_apps">Clear allowed external apps</string><string name="loading">Loading…</string><string name="allowed_vpn_apps_info">Allowed VPN apps: %1$s</string><string name="disallowed_vpn_apps_info">Disallowed VPN apps: %1$s</string><string name="app_no_longer_exists">Package %s is no longer installed, removing it from app allow/disallow list</string><string name="vpn_disallow_radio">VPN is used for all apps but exclude selected</string><string name="vpn_allow_radio">VPN is used for only for selected apps</string><string name="vpn_allow_bypass">Allow apps to bypass the VPN</string><string name="query_delete_remote">Remove remote server entry?</string><string name="keep">Keep</string><string name="delete">Delete</string><string name="add_remote">Add new remote</string><string name="remote_random">Use connection entries in random order on connect</string><string name="remote_no_server_selected">You need to define and enable at least one remote server.</string><string name="server_list">Server List</string><string name="vpn_allowed_apps">Allowed Apps</string><string name="advanced_settings">Advanced Settings</string><string name="payload_options">Payload options</string><string name="tls_settings">TLS Settings</string><string name="no_remote_defined">No remote defined</string><string name="duplicate_vpn">Duplicate VPN profile</string><string name="duplicate_profile_title">Duplicating profile: %s</string><string name="show_log">Show log</string><string name="faq_android_clients">Multiple OpenVPN clients for Android exist. The most common ones are OpenVPN for Android (this client), OpenVPN Connect and OpenVPN Settings.&lt;p>The clients can be grouped into two groups: OpenVPN for Android and OpenVPN Connect use the official VPNService API (Android 4.0+) and require no root and OpenVPN Settings which uses root.&lt;p>OpenVPN for Android is an open source client and developed by Arne Schwabe.  It is targeted at more advanced users and offers many settings and the ability to import profiles from files and to configure/change profiles inside the app. The client is based on the community version of OpenVPN. It is based on the OpenVPN 2.x source code. This client can be seen as the semi officially client of the community. &lt;p>OpenVPN Connect is non open source client that is developed by OpenVPN Technologies, Inc. The client is indented to be general use client and more targeted at the average user and allows the import of OpenVPN profiles. This client is based on the OpenVPN C++ reimplementation of the OpenVPN protocol (This was required to allow OpenVPN Technologies, Inc to publish an iOS OpenVPN app). This client is the official client of the OpenVPN technologies &lt;p> OpenVPN Settings is the oldest of the clients and also a UI for the open source OpenVPN. In contrast to OpenVPN  for Android it requires root and does not use the VPNService API. It does not depend on Android 4.0+</string><string name="faq_androids_clients_title">Differences between the OpenVPN Android clients</string><string name="ignore_multicast_route">Ignoring multicast route: %s</string><string name="ab_only_cidr">Android supports only CIDR routes to the VPN. Since non-CIDR routes are almost never used, OpenVPN for Android will use a /32 for routes that are not CIDR and issue a warning.</string><string name="ab_tethering_44">Tethering works while the VPN is active. The tethered connection will NOT use the VPN.</string><string name="ab_kitkat_mss">Early KitKat version set the wrong MSS value on TCP connections (#61948). Try to enable the mssfix option to workaround this bug.</string><string name="ab_proxy">Android will keep using your proxy settings specified for the mobile/Wi-Fi connection when no DNS servers are set. OpenVPN for Android will warn you about this in the log.<p>When a VPN sets a DNS server Android will not use a proxy. There is no API to set a proxy for a VPN connection.</p></string><string name="ab_lollipop_reinstall">VPN apps may stop working when uninstalled and reinstalled again. For details see #80074</string><string name="ab_not_route_to_vpn">The configured client IP and the IPs in its network mask are not routed to the VPN. OpenVPN works around this bug by explicitly adding a route that corrosponds to the client IP and its netmask</string><string name="ab_persist_tun">Opening a tun device while another tun device is active, which is used for persist-tun support, crashes the VPNServices on the device. A reboot is required to make VPN work again. OpenVPN for Android tries to avoid reopening the tun device and if really needed first closes the current TUN before opening the new TUN device to avoid to crash. This may lead to a short window where packets are sent over the non-VPN connection. Even with this workaround the VPNServices sometimes crashes and requires a reboot of the device.</string><string name="ab_secondary_users">VPN does not work at all for secondary users.</string><string name="ab_kitkat_reconnect">"Multiple users report that the mobile connection/mobile data connection is frequently dropped while using the VPN app. The behaviour seems to affect only some mobile provider/device combination and so far no cause/workaround for the bug could be identified. "</string><string name="ab_vpn_reachability_44">Only destination can be reached over the VPN that are reachable without VPN. IPv6 VPNs does not work at all.</string><string name="ab_only_cidr_title">Non CIDR Routes</string><string name="ab_proxy_title">Proxy behaviour for VPNs</string><string name="ab_lollipop_reinstall_title">Reinstalling VPN apps</string><string name="version_upto">%s and earlier</string><string name="copy_of_profile">Copy of %s</string><string name="ab_not_route_to_vpn_title">Route to the configured IP address</string><string name="ab_kitkat_mss_title">Wrong MSS value for VPN connection</string><string name="ab_secondary_users_title">Secondary tablet users</string><string name="custom_connection_options_warng">Specify custom connection specific options. Use with care</string><string name="custom_connection_options">Custom Options</string><string name="remove_connection_entry">Remove connection entry</string><string name="ab_kitkat_reconnect_title">Random disconnects from mobile network</string><string name="ab_vpn_reachability_44_title">Remote networks not reachable</string><string name="ab_persist_tun_title">Persist tun mode</string><string name="version_and_later">%s and later</string><string name="tls_cipher_alert_title">Connections fails with SSL23_GET_SERVER_HELLO:sslv3 alert handshake failure</string><string name="tls_cipher_alert">Newer OpenVPN for Android versions (0.6.29/March 2015) use a more secure default for the allowed cipher suites (tls-cipher \"DEFAULT:!EXP:!PSK:!SRP:!kRSA\"). Unfortunately, omitting the less secure cipher suites and export cipher suites, especially the omission of cipher suites that do not support Perfect Forward Secrecy (Diffie-Hellman) causes some problems. This usually caused by an well-intentioned but poorly executed attempt to strengthen TLS security by setting tls-cipher on the server or some embedded OSes with stripped down SSL (e.g. MikroTik).\nTo solve this problem the problem, set the tls-cipher settings on the server to reasonable default like tls-cipher \"DEFAULT:!EXP:!PSK:!SRP:!kRSA\". To work around the problem on the client add the custom option tls-cipher DEFAULT on the Android client.</string><string name="message_no_user_edit">This profile has been added from an external app (%s) and has been marked as not user editable.</string><string name="crl_file">Certificate Revocation List</string><string name="service_restarted">Restarting OpenVPN Service (App crashed probably crashed or killed for memory pressure)</string><string name="import_config_error">Importing the config yielded an error, cannot save it</string><string name="Search">Search Server</string><string name="lastdumpdate">(Last dump is %1$d:%2$dh old (%3$s))</string><string name="clear_log_on_connect">Clear log on new connection</string><string name="connect_timeout">Connect Timeout</string><string name="no_allowed_app">No allowed app added. Adding ourselves (%s) to have at least one app in the allowed app list to not allow all apps</string><string name="query_permissions_sdcard">OpenVPN for Android can try to discover the missing file(s) on the sdcard automatically. Tap this message start the permission request.</string><string name="protocol">Protocol</string><string name="enabled_connection_entry">Enabled</string><string name="abi_mismatch">Preferred native ABI precedence of this device (%1$s) and ABI reported by native libraries (%2$s) mismatch</string><string name="permission_revoked">VPN permission revoked by OS (e.g. other VPN program started), stopping VPN</string><string name="pushpeerinfo">Push Peer info</string><string name="pushpeerinfosummary">Send extra information to the server, e.g. SSL version and Android version</string><string name="pw_request_dialog_title">Need %1$s</string><string name="pw_request_dialog_prompt">Please enter the password for profile %1$s</string><string name="menu_use_inline_data">Use inline data</string><string name="export_config_chooser_title">Export configuration file</string><string name="missing_tlsauth">tls-auth file is missing</string><string name="missing_certificates">Missing user certificate or user certifcate key file</string><string name="missing_ca_certificate">Missing CA certificate</string><string name="crl_title">Certifcate Revoke List (optional)</string><string name="reread_log">Reread (%d) log items from log cache file</string><string name="samsung_broken">Even though Samsung phones are among the most selling Android phones, Samsung\'s firmware are also among the most buggy Android firmwares. The bugs are not limited to the VPN operation on these devices but many of them can be workarounded. In the following some of these bugs are described.\n\nDNS does not work unless the DNS server in the VPN range.\n\nOn many Samsung 5.x devices the allowed/disallowed apps feature does not work.\nOn Samsung 6.x VPN is reported not to work unless the VPN app is exempted from Powersave features.</string><string name="samsung_broken_title">Samsung phones</string><string name="novpn_selected">No VPN selected.</string><string name="defaultvpn">Default VPN</string><string name="defaultvpnsummary">VPN used in places where a default VPN needed. These are currently on boot, for Always-On and the Quick Settings Tile.</string><string name="vpnselected">Currently selected VPN: \'%s\'</string><string name="reconnect">Reconnect</string><string name="qs_title">Toggle VPN</string><string name="qs_connect">Connect to %s</string><string name="qs_disconnect">Disconnect %s</string><string name="connectretrymaxmessage">Enter the maximum time between connection attempts. OpenVPN will slowly raise its waiting time after an unsuccessful connection attempt up to this value. Defaults to 300s.</string><string name="connectretrymaxtitle">Maximum time between connection attempts</string><string name="state_waitconnectretry">Waiting %ss seconds between connection attempt</string><string name="nought_alwayson_warning"><![CDATA[If you did not get a VPN confirmation dialog, you have \"Always on VPN\" enabled for another app. In that case only that app is allowed to connect to a VPN. Check under Settings-> Networks more .. -> VPNS]]></string><string name="management_socket_closed">Connection to OpenVPN closed (%s)</string><string name="change_sorting">Change sorting</string><string name="sort">Sort</string><string name="sorted_lru">Profiles sorted by last recently used</string><string name="sorted_az">Profiles sorted by name</string><string name="deprecated_tls_remote">Config uses option tls-remote that was deprecated in 2.3 and finally removed in 2.4</string><string name="auth_failed_behaviour">Behaviour on AUTH_FAILED</string><string name="graph">Graph</string><string name="use_logarithmic_scale">Use logarithmic scale</string><string name="notenoughdata">Not enough data</string><string name="avghour">Average per hour</string><string name="avgmin">Average per minute</string><string name="last5minutes">Last 5 minutes</string><string name="data_in">In</string><string name="data_out">Out</string><string name="bits_per_second">%.0f bit/s</string><string name="kbits_per_second">%.1f kbit/s</string><string name="mbits_per_second">%.1f Mbit/s</string><string name="gbits_per_second">%.1f Gbit/s</string><string name="weakmd">&lt;p>Starting with OpenSSL version 1.1, OpenSSL rejects weak signatures in certificates like
        MD5.&lt;/p>&lt;p>&lt;b>MD5 signatures are completely insecure and should not be used anymore.&lt;/b> MD5
        collisions can be created in &lt;a
        href="https://natmchugh.blogspot.de/2015/02/create-your-own-md5-collisions.html">few hours at a minimal cost.&lt;/a>.
        You should update the VPN certificates as soon as possible.&lt;/p>&lt;p>Unfortunately, older easy-rsa
        distributions included the config option "default_md md5". If you are using an old easy-rsa version, update to
        the &lt;a href="https://github.com/OpenVPN/easy-rsa/releases">latest version&lt;/a>) or change md5 to sha256 and
        regenerate your certificates.&lt;/p>&lt;p>If you really want to use old and broken certificates use the custom
        configuration option tls-cipher "DEFAULT:@SECLEVEL=0" under advanced configuration or as additional line in your
        imported configuration&lt;/p>
    </string><string name="volume_byte">%.0f B</string><string name="volume_kbyte">%.1f kB</string><string name="volume_mbyte">%.1f MB</string><string name="volume_gbyte">%.1f GB</string><string name="channel_name_background">Connection statistics</string><string name="channel_description_background">Ongoing statistics of the established OpenVPN connection</string><string name="channel_name_status">Connection status change</string><string name="channel_description_status">Status changes of the OpenVPN connection (Connecting, authenticating,…)</string><string name="weakmd_title">Weak (MD5) hashes in certificate signature (SSL_CTX_use_certificate md too weak)</string><string name="title_activity_open_sslspeed">OpenSSL Speed Test</string><string name="openssl_cipher_name">OpenSSL cipher names</string><string name="osslspeedtest">OpenSSL Crypto Speed test</string><string name="openssl_error">OpenSSL returned an error</string><string name="running_test">Running test…</string><string name="test_algoirhtms">Test selected algorithms</string><string name="all_app_prompt">An external app tries to control %s. The app requesting access cannot be determined. Allowing this app grants ALL apps access.</string><string name="openvpn3_nostatickeys">The OpenVPN 3 C++ implementation does not support static keys. Please change to OpenVPN 2.x under general settings.</string><string name="openvpn3_pkcs12">Using PKCS12 files directly with OpenVPN 3 C++ implementation is not supported. Please import the pkcs12 files into the Android keystore or change to OpenVPN 2.x under general settings.</string><string name="proxy">Proxy</string><string name="Use_no_proxy">None</string><string name="tor_orbot">Tor (Orbot)</string><string name="openvpn3_socksproxy">OpenVPN 3 C++ implementation does not support connecting via Socks proxy</string><string name="no_orbotfound">Orbot application cannot be found. Please install Orbot or use manual Socks v5 integration.</string><string name="faq_remote_api_title">Remote API</string><string name="faq_remote_api">OpenVPN for Android supports two remote APIs, a sophisticated API using AIDL (remoteEXample in the git repository) and a simple one using Intents. &lt;p>Examples using adb shell and the intents. Replace profilname with your profile name&lt;p>&lt;p> adb shell am start-activity -a android.intent.action.MAIN de.blinkt.openvpn/.api.DisconnectVPN&lt;p> adb shell am start-activity -a android.intent.action.MAIN -e de.blinkt.openvpn.api.profileName Blinkt de.blinkt.openvpn/.api.ConnectVPN</string><string name="enableproxyauth">Enable Proxy Authentication</string><string name="error_orbot_and_proxy_options">Cannot use extra http-proxy-option statement and Orbot integration at the same time</string><string name="info_from_server">Info from server: \'%s\'</string><string name="channel_name_userreq">User interaction required</string><string name="channel_description_userreq">OpenVPN connection requires a user input, e.g. two factor
        authentification
    </string><string name="openurl_requested">Open URL to continue VPN authentication</string><string name="crtext_requested">Answer challenge to continue VPN authentication</string><string name="state_auth_pending">Authentication pending</string><string name="external_authenticator">External Authenticator</string><string name="configure">Configure</string><string name="extauth_not_configured">External Authneticator not configured</string><string name="faq_killswitch_title">Block non VPN connection (\"Killswitch\")</string><string name="faq_killswitch">It is often desired to block connections without VPN. Other apps often use markting terms like \"Killswitch\" or \"Seamless tunnel\" for this feature. OpenVPN and this app offer persist-tun, a feature to implement this functionality.&lt;p>The problem with all these methods offered by apps is that they can only provide best effort and are no complete solutions. On boot, app crashing and other corner cases the app cannot ensure that this block of non VPN connection works. Thus giving the user a false sense of security.&lt;p>The &lt;b>only&lt;/b> reliable way to ensure non VPN connections are blocked is to use Android 8.0 or later and use the \"block connections without VPN\" setting that can be found under Settings > Network &amp; Internet > Advanced/VPN > OpenVPN for Android > Enable Always ON VPN, Enable Block Connections without VPN</string><string name="summary_block_address_families">This option instructs Android to not allow protocols (IPv4/IPv6) if the VPN does not set any IPv4 or IPv6 addresses.</string><string name="title_block_address_families">Block IPv6 (or IPv4) if not used by the VPN</string><string name="install_keychain">Install new certificate</string><string name="as_servername">AS servername</string><string name="request_autologin">Request autologin profile</string><string name="import_from_as">Import Profile from Access Server</string><string name="no_default_vpn_set">Default VPN not set. Please set the Default VPN before enabling this option.</string><string name="internal_web_view">Internal WebView</string></file><file path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\values\styles.xml" qualifiers=""><style name="blinkt.baseTheme" parent="android:Theme.DeviceDefault.Light"/><style name="blinkt" parent="blinkt.baseTheme"/><style name="blinkt.dialog" parent="android:Theme.DeviceDefault.Light.Dialog"/></file><file path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\values\untranslatable.xml" qualifiers=""><string name="copyright_blinktgui" translatable="false">Copyright 2012–2018 Arne Schwabe &lt;<EMAIL>>
    </string><string name="copyright_logo" translatable="false">App Logo design by Helen Beierling
        &lt;<EMAIL>>
    </string><string name="opevpn_copyright" translatable="false">Copyright © 2002–2010 OpenVPN Technologies, Inc. &lt;<EMAIL>>\n

        "OpenVPN" is a trademark of OpenVPN Technologies, Inc.\n
    </string><string name="defaultserver" translatable="false">openvpn.uni-paderborn.de</string><string name="defaultport" translatable="false">1194</string><string name="copyright_file_dialog" translatable="false">File Dialog based on work by Alexander Ponomarev</string><string name="lzo_copyright" translatable="false">Copyright © 1996 – 2011 Markus Franz Xaver Johannes Oberhumer
    </string><string name="copyright_openssl" translatable="false">This product includes software developed by the OpenSSL
        Project for use in the OpenSSL Toolkit\n
        Copyright © 1998-2008 The OpenSSL Project. All rights reserved.\n\n
        This product includes cryptographic software written by Eric Young (<EMAIL>)\n
        Copyright © 1995-1998 Eric Young (<EMAIL>) All rights reserved.
    </string><string name="openvpn" translatable="false">OpenVPN</string><string name="file_dialog" translatable="false">File Dialog</string><string name="lzo" translatable="false">LZO</string><string name="openssl" translatable="false">OpenSSL</string><string name="unknown_state" translatable="false">Unknown state</string><string name="permission_description">Allows another app to control OpenVPN</string><string name="bouncy_castle" translatable="false">Bouncy Castle Crypto APIs</string><string name="copyright_bouncycastle" translatable="false">Copyright © 2000–2012 The Legion Of The Bouncy Castle
        (http://www.bouncycastle.org)
    </string><string-array name="tls_directions_values" translatable="false">
        <item>0</item>
        <item>1</item>
        <item/>
        <item>tls-crypt</item>
        <item>tls-crypt-v2</item>
    </string-array><string-array name="crm_values" translatable="false">
        <item>1</item>
        <item>2</item>
        <item>5</item>
        <item>50</item>
        <item>-1</item>
    </string-array><string name="crash_toast_text">OpenVPN for Android crashed, crash reported</string><string name="state_user_vpn_permission" translatable="false">Waiting for user permission to use VPN API</string><string name="state_user_vpn_password" translatable="false">Waiting for user VPN password</string><string name="state_user_vpn_password_cancelled" translatable="false">VPN password input dialog cancelled</string><string name="state_user_vpn_permission_cancelled" translatable="false">VPN API permission dialog cancelled</string><string name="default_cipherlist_test" translatable="false">aes-256-gcm bf-cbc sha1</string><string name="apprest_uuid_desc">Unique UUID that identifies the profile (example:
        0E910C15–9A85-4DD9-AE0D-E6862392E638). Generate using uuidgen or similar tools
    </string><string name="apprest_uuid">UUID</string><string name="apprest_ovpn_desc">Content of the OpenVPN configuration file. These files are usually have the extension .ovpn (sometimes also .conf) and are plain text multi line configuration files. If your MDM does not support multiline configuration entries, you can also use a base64 encoded string here. A text file can be converted to base64 using openssl base64 -A -in</string><string name="apprest_ovpn">Config</string><string name="apprest_name_desc">Name of the VPN profile</string><string name="apprest_name">Name</string><string name="apprest_vpnlist">List of VPN configurations</string><string name="apprest_vpnconf">VPN configuration</string><string name="apprest_ver">Version of the managed configuration schema (Currently always 1)</string></file><file path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\values-sw600dp\dimens.xml" qualifiers="sw600dp-v13"><bool name="logSildersAlwaysVisible">true</bool></file><file path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\values-sw600dp\styles.xml" qualifiers="sw600dp-v13"><dimen name="stdpadding">16dp</dimen></file><file path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\values-v29\bools.xml" qualifiers="v29"><bool name="supportFileScheme">false</bool></file><file name="app_restrictions" path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\res\xml\app_restrictions.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\generated\res\resValues\debug"/></dataSet><mergedItems><configuration qualifiers=""><declare-styleable name="PagerSlidingTabStrip">
        <attr format="color" name="pstsIndicatorColor"/>
        <attr format="color" name="pstsUnderlineColor"/>
        <attr format="color" name="pstsDividerColor"/>
        <attr format="dimension" name="pstsDividerWidth"/>
        <attr format="dimension" name="pstsIndicatorHeight"/>
        <attr format="dimension" name="pstsUnderlineHeight"/>
        <attr format="dimension" name="pstsDividerPadding"/>
        <attr format="dimension" name="pstsTabPaddingLeftRight"/>
        <attr format="dimension" name="pstsScrollOffset"/>
        <attr format="reference" name="pstsTabBackground"/>
        <attr format="boolean" name="pstsShouldExpand"/>
        <attr format="boolean" name="pstsTextAllCaps"/>
        <attr format="boolean" name="pstsPaddingMiddle"/>
        <attr name="pstsTextStyle">
            <flag name="normal" value="0x0"/>
            <flag name="bold" value="0x1"/>
            <flag name="italic" value="0x2"/>
        </attr>
        <attr name="pstsTextSelectedStyle">
            <flag name="normal" value="0x0"/>
            <flag name="bold" value="0x1"/>
            <flag name="italic" value="0x2"/>
        </attr>
        <attr format="float" name="pstsTextAlpha"/>
        <attr format="float" name="pstsTextSelectedAlpha"/>
    </declare-styleable><declare-styleable name="FileSelectLayout">
       <attr format="string|reference" name="fileTitle"/>
      <attr format="boolean" name="certificate"/>

       <attr format="boolean" name="showClear"/>
   </declare-styleable></configuration></mergedItems></merger>