package com.official.fivegfastvpn.model;

import java.util.List;

/**
 * App Configuration Model for 5G Smart VPN
 * Represents the complete app configuration from the API
 */
public class AppConfig {
    // AdMob Configuration
    private String admobId;
    private String admobBanner;
    private String admobInterstitial;
    private String admobNative;
    private String admobRewarded;
    private String admobOpenad;
    
    // Facebook Ads Configuration
    private String facebookId;
    private String facebookBanner;
    private String facebookInterstitial;
    private String facebookNative;
    private String facebookRewarded;
    
    // Ad Status and Types
    private int adsStatus;
    private String bannerType;
    private String interstitialType;
    private String nativeType;
    private String rewardedType;
    
    // Individual Ad Type Status
    private int bannerEnabled;
    private int interstitialEnabled;
    private int rewardedEnabled;
    private int nativeEnabled;
    private int openadEnabled;
    
    // Additional Settings
    private int rewardTime;
    private int clickLimit;
    private int showFrequency;
    private int testMode;

    // Random Ad Timing Settings
    private int randomAdTimingEnabled;
    private int maxUsageTimeMinutes;
    private String interstitialTimingIntervals;
    
    // Legacy compatibility fields
    private String appId;
    private String banner;
    private String interstitial;
    private String nativeAd;
    private String rewarded;
    private String openad;
    private int active;
    
    // Servers
    private List<Server> servers;
    
    // Metadata
    private String apiVersion;
    private long timestamp;
    private String source;
    private String endpoint;
    
    // Constructors
    public AppConfig() {}
    
    // Getters and Setters
    public String getAdmobId() {
        return admobId;
    }
    
    public void setAdmobId(String admobId) {
        this.admobId = admobId;
    }
    
    public String getAdmobBanner() {
        return admobBanner;
    }
    
    public void setAdmobBanner(String admobBanner) {
        this.admobBanner = admobBanner;
    }
    
    public String getAdmobInterstitial() {
        return admobInterstitial;
    }
    
    public void setAdmobInterstitial(String admobInterstitial) {
        this.admobInterstitial = admobInterstitial;
    }
    
    public String getAdmobNative() {
        return admobNative;
    }
    
    public void setAdmobNative(String admobNative) {
        this.admobNative = admobNative;
    }
    
    public String getAdmobRewarded() {
        return admobRewarded;
    }
    
    public void setAdmobRewarded(String admobRewarded) {
        this.admobRewarded = admobRewarded;
    }
    
    public String getAdmobOpenad() {
        return admobOpenad;
    }
    
    public void setAdmobOpenad(String admobOpenad) {
        this.admobOpenad = admobOpenad;
    }
    
    public String getFacebookId() {
        return facebookId;
    }
    
    public void setFacebookId(String facebookId) {
        this.facebookId = facebookId;
    }
    
    public String getFacebookBanner() {
        return facebookBanner;
    }
    
    public void setFacebookBanner(String facebookBanner) {
        this.facebookBanner = facebookBanner;
    }
    
    public String getFacebookInterstitial() {
        return facebookInterstitial;
    }
    
    public void setFacebookInterstitial(String facebookInterstitial) {
        this.facebookInterstitial = facebookInterstitial;
    }
    
    public String getFacebookNative() {
        return facebookNative;
    }
    
    public void setFacebookNative(String facebookNative) {
        this.facebookNative = facebookNative;
    }
    
    public String getFacebookRewarded() {
        return facebookRewarded;
    }
    
    public void setFacebookRewarded(String facebookRewarded) {
        this.facebookRewarded = facebookRewarded;
    }
    
    public int getAdsStatus() {
        return adsStatus;
    }
    
    public void setAdsStatus(int adsStatus) {
        this.adsStatus = adsStatus;
    }
    
    public String getBannerType() {
        return bannerType;
    }
    
    public void setBannerType(String bannerType) {
        this.bannerType = bannerType;
    }
    
    public String getInterstitialType() {
        return interstitialType;
    }
    
    public void setInterstitialType(String interstitialType) {
        this.interstitialType = interstitialType;
    }
    
    public String getNativeType() {
        return nativeType;
    }
    
    public void setNativeType(String nativeType) {
        this.nativeType = nativeType;
    }
    
    public String getRewardedType() {
        return rewardedType;
    }
    
    public void setRewardedType(String rewardedType) {
        this.rewardedType = rewardedType;
    }
    
    public int getBannerEnabled() {
        return bannerEnabled;
    }
    
    public void setBannerEnabled(int bannerEnabled) {
        this.bannerEnabled = bannerEnabled;
    }
    
    public int getInterstitialEnabled() {
        return interstitialEnabled;
    }
    
    public void setInterstitialEnabled(int interstitialEnabled) {
        this.interstitialEnabled = interstitialEnabled;
    }
    
    public int getRewardedEnabled() {
        return rewardedEnabled;
    }
    
    public void setRewardedEnabled(int rewardedEnabled) {
        this.rewardedEnabled = rewardedEnabled;
    }
    
    public int getNativeEnabled() {
        return nativeEnabled;
    }
    
    public void setNativeEnabled(int nativeEnabled) {
        this.nativeEnabled = nativeEnabled;
    }
    
    public int getOpenadEnabled() {
        return openadEnabled;
    }
    
    public void setOpenadEnabled(int openadEnabled) {
        this.openadEnabled = openadEnabled;
    }
    
    public int getRewardTime() {
        return rewardTime;
    }
    
    public void setRewardTime(int rewardTime) {
        this.rewardTime = rewardTime;
    }
    
    public int getClickLimit() {
        return clickLimit;
    }
    
    public void setClickLimit(int clickLimit) {
        this.clickLimit = clickLimit;
    }
    
    public int getShowFrequency() {
        return showFrequency;
    }
    
    public void setShowFrequency(int showFrequency) {
        this.showFrequency = showFrequency;
    }
    
    public int getTestMode() {
        return testMode;
    }
    
    public void setTestMode(int testMode) {
        this.testMode = testMode;
    }

    // Random Ad Timing Settings getters and setters
    public int getRandomAdTimingEnabled() {
        return randomAdTimingEnabled;
    }

    public void setRandomAdTimingEnabled(int randomAdTimingEnabled) {
        this.randomAdTimingEnabled = randomAdTimingEnabled;
    }

    public int getMaxUsageTimeMinutes() {
        return maxUsageTimeMinutes;
    }

    public void setMaxUsageTimeMinutes(int maxUsageTimeMinutes) {
        this.maxUsageTimeMinutes = maxUsageTimeMinutes;
    }

    public String getInterstitialTimingIntervals() {
        return interstitialTimingIntervals;
    }

    public void setInterstitialTimingIntervals(String interstitialTimingIntervals) {
        this.interstitialTimingIntervals = interstitialTimingIntervals;
    }

    // Legacy getters for backward compatibility
    public String getAppId() {
        return appId != null ? appId : admobId;
    }
    
    public void setAppId(String appId) {
        this.appId = appId;
    }
    
    public String getBanner() {
        return banner != null ? banner : admobBanner;
    }
    
    public void setBanner(String banner) {
        this.banner = banner;
    }
    
    public String getInterstitial() {
        return interstitial != null ? interstitial : admobInterstitial;
    }
    
    public void setInterstitial(String interstitial) {
        this.interstitial = interstitial;
    }
    
    public String getNativeAd() {
        return nativeAd != null ? nativeAd : admobNative;
    }
    
    public void setNativeAd(String nativeAd) {
        this.nativeAd = nativeAd;
    }
    
    public String getRewarded() {
        return rewarded != null ? rewarded : admobRewarded;
    }
    
    public void setRewarded(String rewarded) {
        this.rewarded = rewarded;
    }
    
    public String getOpenad() {
        return openad != null ? openad : admobOpenad;
    }
    
    public void setOpenad(String openad) {
        this.openad = openad;
    }
    
    public int getActive() {
        return active != 0 ? active : adsStatus;
    }
    
    public void setActive(int active) {
        this.active = active;
    }
    
    public List<Server> getServers() {
        return servers;
    }
    
    public void setServers(List<Server> servers) {
        this.servers = servers;
    }
    
    public String getApiVersion() {
        return apiVersion;
    }
    
    public void setApiVersion(String apiVersion) {
        this.apiVersion = apiVersion;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }
    
    public String getSource() {
        return source;
    }
    
    public void setSource(String source) {
        this.source = source;
    }
    
    public String getEndpoint() {
        return endpoint;
    }
    
    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }
    
    /**
     * Check if ads are enabled
     * @return true if any ad type is enabled
     */
    public boolean areAdsEnabled() {
        return getActive() == 1 || getAdsStatus() == 1;
    }
    
    /**
     * Check if test mode is enabled
     * @return true if test mode is enabled
     */
    public boolean isTestMode() {
        return testMode == 1;
    }
}
