1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="de.blinkt.openvpn" >
4
5    <uses-sdk android:minSdkVersion="21" />
6
7    <application>
7-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\AndroidManifest.xml:2:5-23:19
8        <service
8-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\AndroidManifest.xml:3:9-11:19
9            android:name="de.blinkt.openvpn.core.OpenVPNService"
9-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\AndroidManifest.xml:4:13-48
10            android:exported="true"
10-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\AndroidManifest.xml:5:13-36
11            android:foregroundServiceType="dataSync"
11-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\AndroidManifest.xml:6:13-53
12            android:permission="android.permission.BIND_VPN_SERVICE" >
12-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\AndroidManifest.xml:7:13-69
13            <intent-filter>
13-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\AndroidManifest.xml:8:13-10:29
14                <action android:name="android.net.VpnService" />
14-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\AndroidManifest.xml:9:17-65
14-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\AndroidManifest.xml:9:25-62
15            </intent-filter>
16        </service>
17
18        <!-- VpnService-based OpenVPN implementation (V2) -->
19        <service
19-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\AndroidManifest.xml:14:9-22:19
20            android:name="de.blinkt.openvpn.core.OpenVPNServiceV2"
20-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\AndroidManifest.xml:15:13-50
21            android:exported="true"
21-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\AndroidManifest.xml:16:13-36
22            android:foregroundServiceType="dataSync"
22-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\AndroidManifest.xml:17:13-53
23            android:permission="android.permission.BIND_VPN_SERVICE" >
23-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\AndroidManifest.xml:18:13-69
24            <intent-filter>
24-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\AndroidManifest.xml:8:13-10:29
25                <action android:name="android.net.VpnService" />
25-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\AndroidManifest.xml:9:17-65
25-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\src\main\AndroidManifest.xml:9:25-62
26            </intent-filter>
27        </service>
28    </application>
29
30</manifest>
