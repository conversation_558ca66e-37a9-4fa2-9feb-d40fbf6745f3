<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="16dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Header with app logo and name -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/FF5722"
            android:padding="16dp">

            <ImageView
                android:id="@+id/about_app_logo"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_centerHorizontal="true"
                android:src="@mipmap/ic_launcher_round" />

            <TextView
                android:id="@+id/about_app_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/about_app_logo"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="8dp"
                android:fontFamily="sans-serif-medium"
                android:text="@string/app_name"
                android:textColor="@android:color/white"
                android:textSize="22sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/about_app_version"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/about_app_name"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="4dp"
                android:fontFamily="sans-serif"
                android:text="Version 1.0.0"
                android:textColor="@android:color/white"
                android:textSize="14sp" />
        </RelativeLayout>

        <!-- About content -->
        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/about"
                    android:textColor="@android:color/black"
                    android:textSize="16sp" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="16dp"
                    android:background="#E0E0E0" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Features"
                    android:textColor="@android:color/black"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="• Fast and secure VPN connections\n• Multiple server locations\n• No logs policy\n• Easy to use interface\n• 24/7 customer support"
                    android:textColor="@android:color/black"
                    android:textSize="14sp" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="16dp"
                    android:background="#E0E0E0" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="@string/love"
                    android:textColor="@android:color/black"
                    android:textSize="14sp" />
            </LinearLayout>
        </ScrollView>

        <!-- Close button -->
        <Button
            android:id="@+id/about_close_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:background="@color/FF5722"
            android:text="Close"
            android:textColor="@android:color/white" />
    </LinearLayout>
</androidx.cardview.widget.CardView>
