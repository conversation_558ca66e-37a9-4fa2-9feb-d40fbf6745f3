<?php
/**
 * 5G Smart VPN Admin Panel - AdMob Settings
 */

session_start();
require_once 'includes/config.php';

// Define required constants if not already defined
if (!defined('ADMIN_PANEL_NAME')) {
    define('ADMIN_PANEL_NAME', '5G Smart VPN Admin Panel');
}
if (!defined('ADMIN_PANEL_URL')) {
    define('ADMIN_PANEL_URL', '/Svpn5g/5GsmartvpnAdminPanel/admin_new/');
}
if (!defined('ADMIN_PANEL_VERSION')) {
    define('ADMIN_PANEL_VERSION', '3.0');
}

// Simple authentication check
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

$page_title = 'AdMob Settings';
$success_message = '';
$error_message = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $settings = [
        'admob_app_id' => trim($_POST['admob_app_id'] ?? ''),
        'admob_banner_id' => trim($_POST['admob_banner_id'] ?? ''),
        'admob_interstitial_id' => trim($_POST['admob_interstitial_id'] ?? ''),
        'admob_rewarded_id' => trim($_POST['admob_rewarded_id'] ?? ''),
        'admob_native_id' => trim($_POST['admob_native_id'] ?? ''),
        'admob_openad_id' => trim($_POST['admob_openad_id'] ?? ''),
        'facebook_app_id' => trim($_POST['facebook_app_id'] ?? ''),
        'facebook_banner_id' => trim($_POST['facebook_banner_id'] ?? ''),
        'facebook_interstitial_id' => trim($_POST['facebook_interstitial_id'] ?? ''),
        'facebook_rewarded_id' => trim($_POST['facebook_rewarded_id'] ?? ''),
        'facebook_native_id' => trim($_POST['facebook_native_id'] ?? ''),
        'banner_enabled' => isset($_POST['banner_enabled']) ? 1 : 0,
        'interstitial_enabled' => isset($_POST['interstitial_enabled']) ? 1 : 0,
        'rewarded_enabled' => isset($_POST['rewarded_enabled']) ? 1 : 0,
        'native_enabled' => isset($_POST['native_enabled']) ? 1 : 0,
        'openad_enabled' => isset($_POST['openad_enabled']) ? 1 : 0,
        'banner_type' => trim($_POST['banner_type'] ?? 'admob'),
        'interstitial_type' => trim($_POST['interstitial_type'] ?? 'admob'),
        'rewarded_type' => trim($_POST['rewarded_type'] ?? 'admob'),
        'native_type' => trim($_POST['native_type'] ?? 'admob'),
        'test_mode' => isset($_POST['test_mode']) ? 1 : 0,
        'click_limit' => (int)($_POST['click_limit'] ?? 5),
        'show_frequency' => (int)($_POST['show_frequency'] ?? 3),
        'reward_time' => (int)($_POST['reward_time'] ?? 30),
        'random_ad_timing_enabled' => isset($_POST['random_ad_timing_enabled']) ? 1 : 0,
        'max_usage_time_minutes' => (int)($_POST['max_usage_time_minutes'] ?? 10),
        'interstitial_timing_intervals' => trim($_POST['interstitial_timing_intervals'] ?? '3,4,5,9')
    ];

    $success_count = 0;
    $error_count = 0;

    foreach ($settings as $key => $value) {
        try {
            // Check if setting exists
            $check_sql = "SELECT COUNT(*) as count FROM settings WHERE setting_key = ?";
            $check_stmt = $conn->prepare($check_sql);
            $check_stmt->bind_param("s", $key);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();
            $exists = $check_result->fetch_assoc()['count'] > 0;

            if ($exists) {
                // Update existing setting
                $update_sql = "UPDATE settings SET setting_value = ? WHERE setting_key = ?";
                $update_stmt = $conn->prepare($update_sql);
                $update_stmt->bind_param("ss", $value, $key);
                if ($update_stmt->execute()) {
                    $success_count++;
                } else {
                    $error_count++;
                }
            } else {
                // Insert new setting
                $insert_sql = "INSERT INTO settings (setting_key, setting_value) VALUES (?, ?)";
                $insert_stmt = $conn->prepare($insert_sql);
                $insert_stmt->bind_param("ss", $key, $value);
                if ($insert_stmt->execute()) {
                    $success_count++;
                } else {
                    $error_count++;
                }
            }
        } catch (Exception $e) {
            $error_count++;
        }
    }

    if ($error_count === 0) {
        $success_message = 'AdMob settings saved successfully!';
    } else {
        $error_message = "Some settings could not be saved. Saved: $success_count, Failed: $error_count";
    }
}

// Load current settings
$current_settings = [];
try {
    // Check if settings table exists
    $table_check = mysqli_query($conn, "SHOW TABLES LIKE 'settings'");
    if (!$table_check || mysqli_num_rows($table_check) == 0) {
        // Create settings table if it doesn't exist
        $create_table_sql = "
            CREATE TABLE IF NOT EXISTS `settings` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `setting_key` varchar(255) NOT NULL,
                `setting_value` text,
                `description` text,
                `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `setting_key` (`setting_key`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        mysqli_query($conn, $create_table_sql);
    }

    $settings_query = "SELECT setting_key, setting_value FROM settings WHERE setting_key LIKE 'admob_%' OR setting_key LIKE 'facebook_%' OR setting_key LIKE '%_type' OR setting_key IN ('banner_enabled', 'interstitial_enabled', 'rewarded_enabled', 'native_enabled', 'openad_enabled', 'test_mode', 'click_limit', 'show_frequency', 'reward_time', 'interstitial_timing_intervals', 'max_usage_time_minutes', 'random_ad_timing_enabled')";
    $settings_result = mysqli_query($conn, $settings_query);

    if ($settings_result) {
        while ($row = mysqli_fetch_assoc($settings_result)) {
            $current_settings[$row['setting_key']] = $row['setting_value'];
        }
    }
} catch (Exception $e) {
    $error_message = 'Failed to load settings: ' . $e->getMessage();
}

// Default values
$defaults = [
    'admob_app_id' => '',
    'admob_banner_id' => '',
    'admob_interstitial_id' => '',
    'admob_rewarded_id' => '',
    'admob_native_id' => '',
    'admob_openad_id' => '',
    'facebook_app_id' => '',
    'facebook_banner_id' => 'IMG_16_9_APP_INSTALL#YOUR_PLACEMENT_ID',
    'facebook_interstitial_id' => 'IMG_16_9_APP_INSTALL#YOUR_PLACEMENT_ID',
    'facebook_rewarded_id' => 'IMG_16_9_APP_INSTALL#YOUR_PLACEMENT_ID',
    'facebook_native_id' => 'IMG_16_9_APP_INSTALL#YOUR_PLACEMENT_ID',
    'banner_enabled' => 1,
    'interstitial_enabled' => 1,
    'rewarded_enabled' => 0,
    'native_enabled' => 0,
    'openad_enabled' => 1,
    'banner_type' => 'admob',
    'interstitial_type' => 'admob',
    'rewarded_type' => 'admob',
    'native_type' => 'admob',
    'test_mode' => 1,
    'click_limit' => 5,
    'show_frequency' => 3,
    'reward_time' => 30,
    'random_ad_timing_enabled' => 1,
    'max_usage_time_minutes' => 10,
    'interstitial_timing_intervals' => '3,4,5,9'
];

foreach ($defaults as $key => $default) {
    if (!isset($current_settings[$key])) {
        $current_settings[$key] = $default;
    }
}

include 'includes/header.php';
?>

<div class="admin-container">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="content-header">
            <div class="header-left">
                <h1 class="page-title">AdMob Settings</h1>
                <p class="page-subtitle">Configure Google AdMob integration and ad display settings</p>
            </div>
            <div class="header-right">
                <div class="header-actions">
                    <a href="sync_settings.php" class="btn btn-warning">
                        <i class="ri-refresh-line"></i>
                        <span class="hide-mobile">Sync to Legacy</span>
                    </a>
                    <a href="ad-analytics.php" class="btn btn-secondary">
                        <i class="ri-bar-chart-line"></i>
                        <span class="hide-mobile">View Analytics</span>
                    </a>
                </div>
            </div>
        </header>

        <!-- Content Body -->
        <div class="content-body">
            <?php if ($success_message): ?>
            <div class="alert alert-success">
                <i class="ri-check-circle-line"></i>
                <?php echo $success_message; ?>
            </div>
            <?php endif; ?>

            <?php if ($error_message): ?>
            <div class="alert alert-danger">
                <i class="ri-error-warning-line"></i>
                <?php echo $error_message; ?>
            </div>
            <?php endif; ?>

            <form method="POST" class="admob-form">
                <div class="form-grid">
                    <!-- AdMob Configuration -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">AdMob Configuration</h3>
                            <p class="card-subtitle">Enter your Google AdMob app and ad unit IDs</p>
                        </div>
                        <div class="card-body">
                            <div class="form-section">
                                <div class="form-group">
                                    <label for="admob_app_id" class="form-label">AdMob App ID</label>
                                    <input type="text" id="admob_app_id" name="admob_app_id" class="form-control"
                                           value="<?php echo htmlspecialchars($current_settings['admob_app_id']); ?>"
                                           placeholder="ca-app-pub-xxxxxxxxxxxxxxxx~xxxxxxxxxx">
                                    <small class="form-text">Your AdMob application ID from Google AdMob console</small>
                                </div>

                                <div class="form-group">
                                    <label for="admob_banner_id" class="form-label">Banner Ad Unit ID</label>
                                    <input type="text" id="admob_banner_id" name="admob_banner_id" class="form-control"
                                           value="<?php echo htmlspecialchars($current_settings['admob_banner_id']); ?>"
                                           placeholder="ca-app-pub-xxxxxxxxxxxxxxxx/xxxxxxxxxx">
                                </div>

                                <div class="form-group">
                                    <label for="admob_interstitial_id" class="form-label">Interstitial Ad Unit ID</label>
                                    <input type="text" id="admob_interstitial_id" name="admob_interstitial_id" class="form-control"
                                           value="<?php echo htmlspecialchars($current_settings['admob_interstitial_id']); ?>"
                                           placeholder="ca-app-pub-xxxxxxxxxxxxxxxx/xxxxxxxxxx">
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="admob_rewarded_id" class="form-label">Rewarded Ad Unit ID</label>
                                        <input type="text" id="admob_rewarded_id" name="admob_rewarded_id" class="form-control"
                                               value="<?php echo htmlspecialchars($current_settings['admob_rewarded_id']); ?>"
                                               placeholder="ca-app-pub-xxxxxxxxxxxxxxxx/xxxxxxxxxx">
                                    </div>

                                    <div class="form-group">
                                        <label for="admob_native_id" class="form-label">Native Ad Unit ID</label>
                                        <input type="text" id="admob_native_id" name="admob_native_id" class="form-control"
                                               value="<?php echo htmlspecialchars($current_settings['admob_native_id']); ?>"
                                               placeholder="ca-app-pub-xxxxxxxxxxxxxxxx/xxxxxxxxxx">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="admob_openad_id" class="form-label">App Open Ad Unit ID</label>
                                    <input type="text" id="admob_openad_id" name="admob_openad_id" class="form-control"
                                           value="<?php echo htmlspecialchars($current_settings['admob_openad_id']); ?>"
                                           placeholder="ca-app-pub-xxxxxxxxxxxxxxxx/xxxxxxxxxx">
                                    <small class="form-text">App Open ads are displayed when users open your app</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Facebook Ads Configuration -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Facebook Ads Configuration</h3>
                            <p class="card-subtitle">Enter your Facebook Audience Network app and placement IDs</p>
                        </div>
                        <div class="card-body">
                            <div class="form-section">
                                <div class="form-group">
                                    <label for="facebook_app_id" class="form-label">Facebook App ID</label>
                                    <input type="text" id="facebook_app_id" name="facebook_app_id" class="form-control"
                                           value="<?php echo htmlspecialchars($current_settings['facebook_app_id']); ?>"
                                           placeholder="123456789012345">
                                    <small class="form-text">Your Facebook App ID from Facebook Developers console</small>
                                </div>

                                <div class="form-group">
                                    <label for="facebook_banner_id" class="form-label">Facebook Banner Placement ID</label>
                                    <input type="text" id="facebook_banner_id" name="facebook_banner_id" class="form-control"
                                           value="<?php echo htmlspecialchars($current_settings['facebook_banner_id']); ?>"
                                           placeholder="IMG_16_9_APP_INSTALL#YOUR_PLACEMENT_ID">
                                </div>

                                <div class="form-group">
                                    <label for="facebook_interstitial_id" class="form-label">Facebook Interstitial Placement ID</label>
                                    <input type="text" id="facebook_interstitial_id" name="facebook_interstitial_id" class="form-control"
                                           value="<?php echo htmlspecialchars($current_settings['facebook_interstitial_id']); ?>"
                                           placeholder="IMG_16_9_APP_INSTALL#YOUR_PLACEMENT_ID">
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="facebook_rewarded_id" class="form-label">Facebook Rewarded Placement ID</label>
                                        <input type="text" id="facebook_rewarded_id" name="facebook_rewarded_id" class="form-control"
                                               value="<?php echo htmlspecialchars($current_settings['facebook_rewarded_id']); ?>"
                                               placeholder="IMG_16_9_APP_INSTALL#YOUR_PLACEMENT_ID">
                                    </div>

                                    <div class="form-group">
                                        <label for="facebook_native_id" class="form-label">Facebook Native Placement ID</label>
                                        <input type="text" id="facebook_native_id" name="facebook_native_id" class="form-control"
                                               value="<?php echo htmlspecialchars($current_settings['facebook_native_id']); ?>"
                                               placeholder="IMG_16_9_APP_INSTALL#YOUR_PLACEMENT_ID">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Ad Display Settings -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Ad Display Settings</h3>
                            <p class="card-subtitle">Configure when and how ads are displayed</p>
                        </div>
                        <div class="card-body">
                            <div class="form-section">
                                <div class="form-group">
                                    <label class="form-label">Ad Types</label>
                                    <div class="checkbox-grid">
                                        <div class="form-check">
                                            <label class="switch">
                                                <input type="checkbox" name="banner_enabled"
                                                       <?php echo $current_settings['banner_enabled'] ? 'checked' : ''; ?>>
                                                <span class="slider"></span>
                                            </label>
                                            <span class="form-check-label">Enable Banner Ads</span>
                                        </div>

                                        <div class="form-check">
                                            <label class="switch">
                                                <input type="checkbox" name="interstitial_enabled"
                                                       <?php echo $current_settings['interstitial_enabled'] ? 'checked' : ''; ?>>
                                                <span class="slider"></span>
                                            </label>
                                            <span class="form-check-label">Enable Interstitial Ads</span>
                                        </div>

                                        <div class="form-check">
                                            <label class="switch">
                                                <input type="checkbox" name="rewarded_enabled"
                                                       <?php echo $current_settings['rewarded_enabled'] ? 'checked' : ''; ?>>
                                                <span class="slider"></span>
                                            </label>
                                            <span class="form-check-label">Enable Rewarded Ads</span>
                                        </div>

                                        <div class="form-check">
                                            <label class="switch">
                                                <input type="checkbox" name="native_enabled"
                                                       <?php echo $current_settings['native_enabled'] ? 'checked' : ''; ?>>
                                                <span class="slider"></span>
                                            </label>
                                            <span class="form-check-label">Enable Native Ads</span>
                                        </div>

                                        <div class="form-check">
                                            <label class="switch">
                                                <input type="checkbox" name="openad_enabled"
                                                       <?php echo $current_settings['openad_enabled'] ? 'checked' : ''; ?>>
                                                <span class="slider"></span>
                                            </label>
                                            <span class="form-check-label">Enable App Open Ads</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Ad Type Selection -->
                                <div class="form-group">
                                    <label class="form-label">Ad Network Selection</label>
                                    <div class="ad-type-grid">
                                        <div class="ad-type-item">
                                            <label for="banner_type" class="form-label">Banner Ads</label>
                                            <select id="banner_type" name="banner_type" class="form-control form-select">
                                                <option value="admob" <?php echo $current_settings['banner_type'] == 'admob' ? 'selected' : ''; ?>>AdMob</option>
                                                <option value="facebook" <?php echo $current_settings['banner_type'] == 'facebook' ? 'selected' : ''; ?>>Facebook</option>
                                                <option value="off" <?php echo $current_settings['banner_type'] == 'off' ? 'selected' : ''; ?>>Off</option>
                                            </select>
                                        </div>

                                        <div class="ad-type-item">
                                            <label for="interstitial_type" class="form-label">Interstitial Ads</label>
                                            <select id="interstitial_type" name="interstitial_type" class="form-control form-select">
                                                <option value="admob" <?php echo $current_settings['interstitial_type'] == 'admob' ? 'selected' : ''; ?>>AdMob</option>
                                                <option value="facebook" <?php echo $current_settings['interstitial_type'] == 'facebook' ? 'selected' : ''; ?>>Facebook</option>
                                                <option value="off" <?php echo $current_settings['interstitial_type'] == 'off' ? 'selected' : ''; ?>>Off</option>
                                            </select>
                                        </div>

                                        <div class="ad-type-item">
                                            <label for="rewarded_type" class="form-label">Rewarded Ads</label>
                                            <select id="rewarded_type" name="rewarded_type" class="form-control form-select">
                                                <option value="admob" <?php echo $current_settings['rewarded_type'] == 'admob' ? 'selected' : ''; ?>>AdMob</option>
                                                <option value="facebook" <?php echo $current_settings['rewarded_type'] == 'facebook' ? 'selected' : ''; ?>>Facebook</option>
                                                <option value="off" <?php echo $current_settings['rewarded_type'] == 'off' ? 'selected' : ''; ?>>Off</option>
                                            </select>
                                        </div>

                                        <div class="ad-type-item">
                                            <label for="native_type" class="form-label">Native Ads</label>
                                            <select id="native_type" name="native_type" class="form-control form-select">
                                                <option value="admob" <?php echo $current_settings['native_type'] == 'admob' ? 'selected' : ''; ?>>AdMob</option>
                                                <option value="facebook" <?php echo $current_settings['native_type'] == 'facebook' ? 'selected' : ''; ?>>Facebook</option>
                                                <option value="off" <?php echo $current_settings['native_type'] == 'off' ? 'selected' : ''; ?>>Off</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="show_frequency" class="form-label">Show Frequency</label>
                                        <select id="show_frequency" name="show_frequency" class="form-control form-select">
                                            <option value="1" <?php echo $current_settings['show_frequency'] == 1 ? 'selected' : ''; ?>>Every connection</option>
                                            <option value="2" <?php echo $current_settings['show_frequency'] == 2 ? 'selected' : ''; ?>>Every 2 connections</option>
                                            <option value="3" <?php echo $current_settings['show_frequency'] == 3 ? 'selected' : ''; ?>>Every 3 connections</option>
                                            <option value="5" <?php echo $current_settings['show_frequency'] == 5 ? 'selected' : ''; ?>>Every 5 connections</option>
                                            <option value="10" <?php echo $current_settings['show_frequency'] == 10 ? 'selected' : ''; ?>>Every 10 connections</option>
                                        </select>
                                        <small class="form-text">How often to show interstitial ads</small>
                                    </div>

                                    <div class="form-group">
                                        <label for="click_limit" class="form-label">Daily Click Limit</label>
                                        <input type="number" id="click_limit" name="click_limit" class="form-control"
                                               value="<?php echo htmlspecialchars($current_settings['click_limit']); ?>"
                                               min="1" max="100" placeholder="5">
                                        <small class="form-text">Maximum ad clicks per user per day</small>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="reward_time" class="form-label">Reward Time (Minutes)</label>
                                    <input type="number" id="reward_time" name="reward_time" class="form-control"
                                           value="<?php echo htmlspecialchars($current_settings['reward_time']); ?>"
                                           min="1" max="120" placeholder="30">
                                    <small class="form-text">Time in minutes for rewarded ad rewards</small>
                                </div>

                                <!-- Random Ad Timing Settings -->
                                <div class="form-group">
                                    <label class="form-label">Random Ad Timing</label>
                                    <div class="form-check">
                                        <label class="switch">
                                            <input type="checkbox" name="random_ad_timing_enabled"
                                                   <?php echo $current_settings['random_ad_timing_enabled'] ? 'checked' : ''; ?>>
                                            <span class="slider"></span>
                                        </label>
                                        <span class="form-check-label">Enable Random Interstitial Ad Timing</span>
                                    </div>
                                    <small class="form-text">Show interstitial ads at random intervals instead of fixed patterns</small>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="max_usage_time_minutes" class="form-label">Maximum Usage Time (Minutes)</label>
                                        <input type="number" id="max_usage_time_minutes" name="max_usage_time_minutes" class="form-control"
                                               value="<?php echo htmlspecialchars($current_settings['max_usage_time_minutes']); ?>"
                                               min="1" max="60" placeholder="10">
                                        <small class="form-text">Maximum VPN usage time before showing ads</small>
                                    </div>

                                    <div class="form-group">
                                        <label for="interstitial_timing_intervals" class="form-label">Random Timing Intervals (Minutes)</label>
                                        <input type="text" id="interstitial_timing_intervals" name="interstitial_timing_intervals" class="form-control"
                                               value="<?php echo htmlspecialchars($current_settings['interstitial_timing_intervals']); ?>"
                                               placeholder="3,4,5,9">
                                        <small class="form-text">Comma-separated list of random intervals in minutes (e.g., 3,4,5,9)</small>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">Development Settings</label>
                                    <div class="form-check">
                                        <label class="switch">
                                            <input type="checkbox" name="test_mode"
                                                   <?php echo $current_settings['test_mode'] ? 'checked' : ''; ?>>
                                            <span class="slider"></span>
                                        </label>
                                        <span class="form-check-label">Test Mode (Use test ad units)</span>
                                    </div>
                                    <small class="form-text">Enable this during development to show test ads</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="ri-save-line"></i>
                        Save Settings
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="resetForm()">
                        <i class="ri-refresh-line"></i>
                        Reset
                    </button>
                </div>
            </form>
        </div>
    </main>
</div>

<script>
function resetForm() {
    if (confirm('Are you sure you want to reset all settings? This will reload the page.')) {
        location.reload();
    }
}

// Add some visual feedback for form changes
document.querySelectorAll('input, select').forEach(element => {
    element.addEventListener('change', function() {
        this.classList.add('changed');
    });
});
</script>

<style>
.checkbox-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 0.5rem;
}

.form-check {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: var(--gray-50);
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--gray-200);
}

.ad-type-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 0.5rem;
}

.ad-type-item {
    padding: 1rem;
    background: var(--gray-50);
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--gray-200);
}

.ad-type-item .form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--gray-700);
}

.changed {
    border-color: var(--warning-500) !important;
    box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.1) !important;
}
</style>

<?php include 'includes/footer.php'; ?>
