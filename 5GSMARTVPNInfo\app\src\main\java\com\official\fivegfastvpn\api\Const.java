package com.official.fivegfastvpn.api;

/**
 * 5G Smart VPN API Constants
 * Updated for modern admin panel integration
 * Developer: <PERSON><PERSON> <PERSON><PERSON><PERSON>
 */
public class Const {
    // Base URL for the modern admin panel API
   public static final String ADMIN_BASE = "http://192.168.0.106/Svpn5g/5GsmartvpnAdminPanel/admin_new/";
    //public static final String ADMIN_BASE = "https://5gsvpn.com/admin_new/";
    public static final String API_BASE = ADMIN_BASE + "api/";

    // Legacy base URL (kept for backward compatibility)
    public static final String base = ADMIN_BASE;

    // Modern API Endpoints
    public static final String CONFIG_API = API_BASE + "config.php";
    public static final String SERVERS_API = API_BASE + "servers.php";
    public static final String SETTINGS_API = API_BASE + "settings.php";
    public static final String CUSTOM_ADS_API = API_BASE + "custom_ads.php";
    public static final String TRACK_AD_API = API_BASE + "track_ad.php";
    public static final String IP_API = API_BASE + "ip.php";
    public static final String STATUS_API = API_BASE + "status.php";
    public static final String LEGACY_API = API_BASE + "legacy.php";

    // Legacy endpoints (for backward compatibility)
    public static final String api = CONFIG_API;
    public static final String ip = IP_API;
    public static final String policy = base + "policy.php";
    public static String SERVERS = ""; // Remove final to allow assignment

    // Notification API endpoints
    public static final String NOTIFICATIONS_LIST = ADMIN_BASE + "notifications/api/list.php";
    public static final String NOTIFICATIONS_SEND = ADMIN_BASE + "notifications/api/send.php";

    // API Authentication
    public static final String API_SECRET_KEY = "5g-smart-vpn-api-key-2024-secure"; // Matches server configuration
    public static final String apiKey = "RGV2ZWxvcGVkIEJ5IE1kIEthd3NhciBBbGk="; // Legacy key

    // API Configuration
    public static final int API_TIMEOUT_SECONDS = 30;
    public static final int API_RETRY_COUNT = 3;
    public static final String API_VERSION = "3.0";
}