// Generated by view binder compiler. Do not edit!
package com.official.fivegfastvpn.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.RelativeLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.facebook.ads.NativeAdLayout;
import com.official.fivegfastvpn.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class NativeItemAdsContainerBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final NativeAdLayout nativeAdContainer;

  @NonNull
  public final FrameLayout nativeFrame;

  @NonNull
  public final RelativeLayout nativeMainContainer;

  @NonNull
  public final RelativeLayout rlNoadView;

  private NativeItemAdsContainerBinding(@NonNull RelativeLayout rootView,
      @NonNull NativeAdLayout nativeAdContainer, @NonNull FrameLayout nativeFrame,
      @NonNull RelativeLayout nativeMainContainer, @NonNull RelativeLayout rlNoadView) {
    this.rootView = rootView;
    this.nativeAdContainer = nativeAdContainer;
    this.nativeFrame = nativeFrame;
    this.nativeMainContainer = nativeMainContainer;
    this.rlNoadView = rlNoadView;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static NativeItemAdsContainerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static NativeItemAdsContainerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.native_item_ads_container, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static NativeItemAdsContainerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.native_ad_container;
      NativeAdLayout nativeAdContainer = ViewBindings.findChildViewById(rootView, id);
      if (nativeAdContainer == null) {
        break missingId;
      }

      id = R.id.native_frame;
      FrameLayout nativeFrame = ViewBindings.findChildViewById(rootView, id);
      if (nativeFrame == null) {
        break missingId;
      }

      RelativeLayout nativeMainContainer = (RelativeLayout) rootView;

      id = R.id.rlNoadView;
      RelativeLayout rlNoadView = ViewBindings.findChildViewById(rootView, id);
      if (rlNoadView == null) {
        break missingId;
      }

      return new NativeItemAdsContainerBinding((RelativeLayout) rootView, nativeAdContainer,
          nativeFrame, nativeMainContainer, rlNoadView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
